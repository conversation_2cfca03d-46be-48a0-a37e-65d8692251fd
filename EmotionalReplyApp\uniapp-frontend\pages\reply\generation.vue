<template>
  <view class="container">
    <!-- 原始消息显示 -->
    <view class="original-message">
      <text class="label">收到的消息：</text>
      <text class="message-text">{{ originalMessage }}</text>
    </view>
    
    <!-- 情感分析结果 -->
    <view class="emotion-analysis">
      <text class="section-title">情感分析</text>

      <!-- 情感分析加载中 -->
      <view v-if="emotionLoading" class="emotion-loading">
        <view class="loading-spinner"></view>
        <text class="loading-text">正在分析情感...</text>
      </view>

      <!-- 情感分析结果 -->
      <view v-else-if="emotionResult" class="emotion-tags">
        <text class="emotion-tag">{{ emotionResult.emotion }}</text>
        <text class="confidence">置信度: {{ Math.round(emotionResult.confidence * 100) }}%</text>
      </view>

      <!-- 等待开始 -->
      <view v-else class="emotion-waiting">
        <text>等待分析...</text>
      </view>
    </view>
    
    <!-- 回复建议 -->
    <view class="reply-suggestions">
      <text class="section-title">回复建议</text>

      <!-- 等待情感分析完成 -->
      <view v-if="!emotionResult && !emotionLoading" class="reply-waiting">
        <text class="waiting-text">等待情感分析完成后开始生成回复...</text>
      </view>

      <!-- 回复生成加载中 -->
      <view v-else-if="replyLoading" class="loading">
        <view class="loading-spinner"></view>
        <text class="loading-text">正在生成回复建议...</text>
        <text class="loading-tip">基于情感分析结果生成多种风格的回复</text>
      </view>

      <!-- 回复列表 -->
      <view v-else-if="replyList.length > 0" class="reply-list">
        <view
          v-for="(reply, index) in replyList"
          :key="index"
          class="reply-item"
        >
          <view class="reply-header">
            <text class="style-tag">{{ getStyleName(reply.style) }}</text>
            <view class="actions">
              <text class="copy-btn" @click="copyReply(reply.content)">复制</text>
              <text class="like-btn" @click="likeReply(reply)">👍</text>
            </view>
          </view>
          <text class="reply-content">{{ reply.content }}</text>
        </view>
      </view>

      <!-- 等待开始生成 -->
      <view v-else class="reply-waiting">
        <text class="waiting-text">等待生成回复建议...</text>
      </view>
    </view>
    
    <!-- 底部操作 -->
    <view class="bottom-actions">
      <button class="regenerate-btn" @click="regenerateReplies">
        <text>重新生成</text>
      </button>
      <button class="new-message-btn" @click="inputNewMessage">
        <text>输入新消息</text>
      </button>
    </view>
  </view>
</template>

<script>
import { HistoryManager } from '../../utils/storage.js'
import { analyzeEmotion } from '../../api/emotion.js'
import { UserManager } from '../../utils/user.js'

export default {
  name: 'ReplyGeneration',

  data() {
    return {
      originalMessage: '',
      loading: false,
      emotionLoading: false,
      replyLoading: false,
      emotionResult: null,
      replyList: []
    }
  },
  
  computed: {
    // 是否已登录
    isLoggedIn() {
      return UserManager.isLoggedIn()
    }
  },

  onLoad(options) {
    // 检查登录状态
    if (!this.isLoggedIn) {
      uni.showModal({
        title: '需要登录',
        content: '请先登录以使用智能回复功能',
        showCancel: false,
        success: () => {
          uni.navigateTo({
            url: '/pages/login/login'
          })
        }
      })
      return
    }

    if (options.message) {
      this.originalMessage = decodeURIComponent(options.message)
      this.generateReplies()
    }
  },
  
  methods: {
    // 生成回复
    async generateReplies() {
      // 重置状态
      this.emotionResult = null
      this.replyList = []

      // 第一阶段：显示情感分析加载
      this.emotionLoading = true
      this.replyLoading = false

      try {
        // 获取用户的回复风格设置
        const userId = UserManager.getCurrentUserId()
        let replyStyles = ['romantic', 'humorous', 'high_eq'] // 默认风格

        if (userId) {
          try {
            const { getUserReplyPreferences } = await import('../../api/user.js')
            const preferences = await getUserReplyPreferences(userId)
            console.log('获取到的用户偏好设置:', preferences)

            if (preferences && (preferences.data || preferences.generationMode)) {
              // 兼容两种数据格式：包装在data中的和直接返回的
              const settingsData = preferences.data || preferences

              // 根据用户的生成模式决定使用哪些风格
              const mode = settingsData.generationMode || 'smart'
              const count = settingsData.preferredCount || 2

              console.log('用户设置 - 模式:', mode, '数量:', count, '主要风格:', settingsData.primaryStyle)

              if (mode === 'single') {
                replyStyles = [settingsData.primaryStyle || 'romantic']
                console.log('单一模式，使用风格:', replyStyles)
              } else if (mode === 'custom') {
                const userStyles = JSON.parse(settingsData.replyStyles || '["romantic", "humorous", "high_eq"]')
                replyStyles = userStyles.slice(0, count)
                console.log('自定义模式，使用风格:', replyStyles)
              } else {
                // smart模式，使用默认的智能选择（后端会处理）
                replyStyles = [] // 空数组表示让后端智能选择
                console.log('智能模式，让后端选择风格')
              }
            } else {
              console.log('没有获取到用户偏好设置，使用默认风格')
            }
          } catch (error) {
            console.error('获取用户回复偏好失败:', error)
            // 获取用户回复偏好失败，使用默认设置
          }
        }

        console.log('准备调用API，使用的风格:', replyStyles)

        // 调用API（这个API会同时返回情感分析和回复生成结果）
        const response = await analyzeEmotion(
          this.originalMessage,
          replyStyles,
          true
        )

        console.log('API响应:', response)
        console.log('回复选项数量:', response.replyOptions ? response.replyOptions.length : 0)

        // 第一阶段完成：显示情感分析结果
        this.emotionResult = response.emotionResult
        this.emotionLoading = false

        // 给用户一点时间查看情感分析结果
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 第二阶段：显示回复生成加载
        this.replyLoading = true

        // 模拟回复生成过程（实际上数据已经有了）
        await new Promise(resolve => setTimeout(resolve, 1500))

        // 处理回复数据
        this.replyList = response.replyOptions || []
        console.log('处理后的回复列表数量:', this.replyList.length)

        // 转换回复数据格式以适配前端显示
        this.replyList = this.replyList.map(option => ({
          id: Date.now() + Math.random(),
          content: option.content,
          style: option.style,
          styleName: option.styleName,
          recommendation: option.recommendation || 3,
          confidence: option.confidence || 0.8
        }))

        // 第二阶段完成
        this.replyLoading = false

      } catch (error) {
        console.error('API调用失败:', error)

        // 重置加载状态
        this.emotionLoading = false
        this.replyLoading = false

        // 获取具体的错误信息
        let errorMessage = '网络连接失败，请检查网络后重试'

        if (error && error.message) {
          errorMessage = error.message
        } else if (error && error.data && error.data.message) {
          errorMessage = error.data.message
        } else if (error && typeof error === 'string') {
          errorMessage = error
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        })
      }
    },
    
    // 重新生成回复
    regenerateReplies() {
      this.generateReplies()
    },
    
    // 复制回复
    copyReply(content) {
      uni.setClipboardData({
        data: content,
        success: () => {
          uni.showToast({
            title: '已复制到剪贴板',
            icon: 'success'
          })
        }
      })
    },
    
    // 点赞回复
    likeReply(reply) {
      // 这里可以添加点赞统计逻辑
      uni.showToast({
        title: '感谢反馈！',
        icon: 'success'
      })
    },
    
    // 输入新消息
    inputNewMessage() {
      uni.navigateBack()
    },
    
    // 获取风格名称
    getStyleName(style) {
      const styleMap = {
        warm_caring: '暖男',
        humorous: '玩梗',
        romantic: '撩妹',
        high_eq: '高情商',
        direct: '直接',
        mature: '成熟稳重',
        gentle: '温柔大叔',
        dominant: '霸道总裁',
        literary: '文艺风格',
        detailed: '话痨风格',
        rational: '理性分析',
        concise: '简洁直接'
      }
      return styleMap[style] || style
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.original-message {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .label {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 16rpx;
  }
  
  .message-text {
    font-size: 32rpx;
    color: #333;
    line-height: 1.5;
  }
}

.emotion-analysis {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }
  
  .emotion-loading {
    text-align: center;
    padding: 30rpx 0;

    .loading-spinner {
      width: 40rpx;
      height: 40rpx;
      border: 3rpx solid #f3f3f3;
      border-top: 3rpx solid #2196F3;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 20rpx;
    }

    .loading-text {
      font-size: 26rpx;
      color: #666;
    }
  }

  .emotion-waiting {
    text-align: center;
    padding: 30rpx 0;
    color: #999;
    font-size: 26rpx;
  }

  .emotion-tags {
    display: flex;
    align-items: center;

    .emotion-tag {
      background: #2196F3;
      color: white;
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      font-size: 24rpx;
      margin-right: 20rpx;
    }

    .confidence {
      font-size: 24rpx;
      color: #666;
    }
  }
}

.reply-suggestions {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 100rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 30rpx;
  }
  
  .reply-waiting {
    text-align: center;
    padding: 60rpx 0;

    .waiting-text {
      font-size: 26rpx;
      color: #999;
    }
  }

  .loading {
    text-align: center;
    padding: 60rpx 0;

    .loading-spinner {
      width: 60rpx;
      height: 60rpx;
      border: 4rpx solid #f3f3f3;
      border-top: 4rpx solid #2196F3;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 30rpx;
    }

    .loading-text {
      display: block;
      font-size: 28rpx;
      color: #333;
      margin-bottom: 16rpx;
    }

    .loading-tip {
      display: block;
      font-size: 24rpx;
      color: #999;
    }
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  .reply-list {
    .reply-item {
      border: 2rpx solid #f0f0f0;
      border-radius: 12rpx;
      padding: 24rpx;
      margin-bottom: 20rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .reply-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16rpx;
        
        .style-tag {
          background: rgba(33, 150, 243, 0.1);
          color: #2196F3;
          padding: 6rpx 12rpx;
          border-radius: 12rpx;
          font-size: 22rpx;
        }
        
        .actions {
          display: flex;
          align-items: center;
          
          .copy-btn, .like-btn {
            margin-left: 20rpx;
            font-size: 24rpx;
            color: #2196F3;
          }
        }
      }
      
      .reply-content {
        font-size: 28rpx;
        color: #333;
        line-height: 1.6;
      }
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx;
  display: flex;
  gap: 20rpx;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
  
  .regenerate-btn, .new-message-btn {
    flex: 1;
    height: 80rpx;
    border-radius: 40rpx;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    
    text {
      font-size: 28rpx;
      font-weight: bold;
    }
  }
  
  .regenerate-btn {
    background: #f0f0f0;
    
    text {
      color: #333;
    }
  }
  
  .new-message-btn {
    background: linear-gradient(135deg, #2196F3, #21CBF3);
    
    text {
      color: white;
    }
  }
}
</style>
