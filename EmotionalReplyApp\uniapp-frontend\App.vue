<template>
  <view id="app">
    <!-- 全局加载提示 -->
    <view
      v-if="globalLoading"
      class="global-loading"
    >
      <text>正在加载...</text>
    </view>
  </view>
</template>

<script>
import { UserManager } from './utils/user.js'
import themeManager from './utils/theme.js'

export default {
  name: 'App',

  data() {
    return {
      globalLoading: false,
      themeClass: 'theme-auto'
    }
  },

  onLaunch() {
    console.log('App Launch')
    this.initApp()
    this.initTheme()
  },

  onShow() {
    console.log('App Show')
    // 每次应用显示时检查登录状态
    this.checkAuthAndRedirect()
  },

  onHide() {
    console.log('App Hide')
  },

  methods: {
    // 初始化应用
    async initApp() {
      try {
        this.globalLoading = true

        // 检查登录状态并重定向
        await this.checkAuthAndRedirect()

        console.log('App initialized successfully')

      } catch (error) {
        console.error('App initialization failed:', error)
        uni.showToast({
          title: '应用初始化失败',
          icon: 'none'
        })
      } finally {
        this.globalLoading = false
      }
    },

    // 检查登录状态并重定向
    async checkAuthAndRedirect() {
      // 延迟一下确保页面加载完成
      await this.delay(100)

      const isLoggedIn = UserManager.isLoggedIn()
      console.log('检查登录状态:', isLoggedIn)

      // 获取当前页面路径
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      const currentRoute = currentPage ? currentPage.route : ''

      // 如果未登录且不在登录相关页面，强制跳转到登录页
      if (!isLoggedIn && !this.isAuthPage(currentRoute)) {
        console.log('未登录，跳转到登录页')
        uni.reLaunch({
          url: '/pages/login/login'
        })
        return
      }

      // 如果已登录且在登录页面，跳转到首页
      if (isLoggedIn && this.isAuthPage(currentRoute)) {
        console.log('已登录，跳转到首页')
        uni.reLaunch({
          url: '/pages/index/index'
        })
        return
      }
    },

    // 判断是否为登录相关页面
    isAuthPage(route) {
      const authPages = [
        'pages/login/login',
        'pages/user/register',
        'pages/user/forgot-password'
      ]
      return authPages.includes(route)
    },

    // 延迟函数
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    },

    // 初始化主题
    initTheme() {
      try {
        // 初始化主题管理器
        themeManager.init()

        // 监听系统主题变化
        themeManager.watchSystemTheme()

        // 设置全局主题类
        this.themeClass = `theme-${themeManager.getCurrentTheme()}`

        // 设置全局数据供其他页面使用
        this.globalData = this.globalData || {}
        this.globalData.themeManager = themeManager
        this.globalData.themeClass = this.themeClass

        console.log('主题初始化完成:', this.themeClass)

      } catch (error) {
        console.error('主题初始化失败:', error)
      }
    }
  }
}
</script>

<style lang="scss">
/* 导入主题样式 */
@import './styles/theme.scss';

/* 全局样式 */
page {
  background-color: var(--bg-color, #f8f9fa);
  color: var(--text-color, #333333);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 通用类 */
.text-center {
  text-align: center;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 颜色类 */
.text-primary {
  color: #2196F3;
}

.text-muted {
  color: #666;
}

/* 按钮样式 */
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
}

.btn-primary {
  background: linear-gradient(135deg, #2196F3, #21CBF3);
  color: white;
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

#app {
  height: 100vh;
  position: relative;
}

.global-loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;

  text {
    font-size: 28rpx;
    color: #2196F3;
  }
}
</style>
