<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API响应测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1976D2;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .highlight {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        .field-analysis {
            background: #e3f2fd;
            border-color: #bbdefb;
            color: #0d47a1;
        }
    </style>
</head>
<body>
    <h1>🔧 API响应测试工具</h1>
    
    <div class="test-section">
        <h2>用户回复偏好设置API测试</h2>
        <p>测试 <code>/user/settings/{userId}/reply-styles</code> 接口</p>
        
        <button onclick="testUserAPI(1)">测试用户1</button>
        <button onclick="testUserAPI(2)">测试用户2</button>
        <button onclick="testUserAPI(3)">测试用户3</button>
        <button onclick="testAllUsers()">测试所有用户</button>
        
        <div id="apiResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>字段分析</h2>
        <p>分析API返回的字段结构和值</p>
        
        <button onclick="analyzeFields()">分析字段结构</button>
        
        <div id="fieldAnalysis" class="result field-analysis"></div>
    </div>

    <div class="test-section">
        <h2>前端模式映射测试</h2>
        <p>测试前端的模式文本映射逻辑</p>
        
        <button onclick="testModeMapping()">测试模式映射</button>
        
        <div id="mappingResult" class="result"></div>
    </div>

    <script>
        const BASE_URL = 'http://localhost:8080';
        let lastApiResponse = null;

        async function makeRequest(url, method = 'GET', data = null) {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (data) {
                    options.body = JSON.stringify(data);
                }

                const response = await fetch(BASE_URL + url, options);
                const result = await response.json();
                return { success: true, data: result, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function testUserAPI(userId) {
            const result = await makeRequest(`/user/settings/${userId}/reply-styles`);
            lastApiResponse = result;
            
            let output = `用户 ${userId} 的API响应:\n\n`;
            
            if (result.success) {
                output += `HTTP状态: ${result.status}\n`;
                output += `完整响应:\n${JSON.stringify(result.data, null, 2)}\n\n`;
                
                if (result.data.success && result.data.data) {
                    const data = result.data.data;
                    output += `关键字段分析:\n`;
                    output += `- generationMode: "${data.generationMode}" (类型: ${typeof data.generationMode})\n`;
                    output += `- preferredCount: ${data.preferredCount}\n`;
                    output += `- primaryStyle: "${data.primaryStyle}"\n`;
                    output += `- replyStyles: ${data.replyStyles}\n\n`;
                    
                    // 模拟前端的模式文本转换
                    const modeMap = {
                        'smart': '>',
                        'custom': '自定义风格',
                        'single': '单一风格'
                    };
                    const modeText = modeMap[data.generationMode] || '>';
                    output += `前端应该显示: "${modeText}"`;
                } else {
                    output += `API返回失败: ${result.data.message || '未知错误'}`;
                }
            } else {
                output += `请求失败: ${result.error}`;
            }
            
            document.getElementById('apiResult').textContent = output;
            document.getElementById('apiResult').className = result.success ? 'result success' : 'result error';
        }

        async function testAllUsers() {
            let output = '所有用户的generationMode对比:\n\n';
            
            for (let userId = 1; userId <= 3; userId++) {
                const result = await makeRequest(`/user/settings/${userId}/reply-styles`);
                
                output += `用户 ${userId}: `;
                if (result.success && result.data.success && result.data.data) {
                    const mode = result.data.data.generationMode;
                    const modeMap = {
                        'smart': '>',
                        'custom': '自定义风格',
                        'single': '单一风格'
                    };
                    const displayText = modeMap[mode] || '>';
                    output += `${mode} → "${displayText}"\n`;
                } else {
                    output += `获取失败\n`;
                }
            }
            
            document.getElementById('apiResult').textContent = output;
            document.getElementById('apiResult').className = 'result highlight';
        }

        function analyzeFields() {
            if (!lastApiResponse || !lastApiResponse.success) {
                document.getElementById('fieldAnalysis').textContent = '请先调用API获取数据';
                return;
            }

            const data = lastApiResponse.data.data;
            let output = '字段详细分析:\n\n';
            
            Object.keys(data).forEach(key => {
                const value = data[key];
                output += `字段: ${key}\n`;
                output += `  值: ${JSON.stringify(value)}\n`;
                output += `  类型: ${typeof value}\n`;
                output += `  长度: ${value && value.length ? value.length : 'N/A'}\n\n`;
            });

            // 特别检查generationMode
            if (data.generationMode) {
                output += `generationMode特别检查:\n`;
                output += `  原始值: "${data.generationMode}"\n`;
                output += `  去空格后: "${data.generationMode.trim()}"\n`;
                output += `  字符编码: ${Array.from(data.generationMode).map(c => c.charCodeAt(0)).join(', ')}\n`;
                output += `  是否等于'custom': ${data.generationMode === 'custom'}\n`;
                output += `  是否等于'single': ${data.generationMode === 'single'}\n`;
                output += `  是否等于'smart': ${data.generationMode === 'smart'}\n`;
            }

            document.getElementById('fieldAnalysis').textContent = output;
        }

        function testModeMapping() {
            const testCases = [
                { input: 'smart', expected: '>' },
                { input: 'custom', expected: '自定义风格' },
                { input: 'single', expected: '单一风格' },
                { input: null, expected: '>' },
                { input: undefined, expected: '>' },
                { input: '', expected: '>' },
                { input: 'unknown', expected: '>' }
            ];

            const modeMap = {
                'smart': '>',
                'custom': '自定义风格',
                'single': '单一风格'
            };

            let output = '模式映射测试结果:\n\n';
            testCases.forEach((testCase, index) => {
                const result = modeMap[testCase.input] || '>';
                const passed = result === testCase.expected;
                output += `测试 ${index + 1}: ${passed ? '✅' : '❌'}\n`;
                output += `  输入: ${JSON.stringify(testCase.input)}\n`;
                output += `  期望: "${testCase.expected}"\n`;
                output += `  实际: "${result}"\n\n`;
            });

            document.getElementById('mappingResult').textContent = output;
            document.getElementById('mappingResult').className = 'result success';
        }

        // 页面加载时显示说明
        window.onload = function() {
            console.log('API响应测试工具已加载');
            console.log('请确保后端服务运行在 http://localhost:8080');
            
            document.getElementById('apiResult').textContent = '点击按钮开始测试API响应...';
            document.getElementById('fieldAnalysis').textContent = '先调用API，然后点击分析字段结构...';
            document.getElementById('mappingResult').textContent = '点击测试模式映射逻辑...';
        };
    </script>
</body>
</html>
