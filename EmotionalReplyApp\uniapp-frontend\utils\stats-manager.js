/**
 * 统计管理工具类
 */

import { UserManager } from './user.js'

export class StatsManager {
  /**
   * 记录用户使用
   * @param {string} feature - 功能名称
   * @returns {Promise<boolean>} 是否成功
   */
  static async recordUsage(feature = 'general') {
    try {
      const currentUserId = UserManager.getCurrentUserId()
      if (!currentUserId) {
        console.log('用户未登录，无法记录使用统计')
        return false
      }

      // 导入API
      const { incrementUsage } = await import('../api/user.js')
      
      // 调用后端接口增加使用次数
      const result = await incrementUsage(currentUserId)
      
      if (result && result.success) {
        console.log(`记录用户使用成功: feature=${feature}, userId=${currentUserId}`)
        return true
      } else {
        console.warn(`记录用户使用失败: ${result?.message || '未知错误'}`)
        return false
      }
    } catch (error) {
      console.error('记录用户使用异常:', error)
      return false
    }
  }

  /**
   * 检查用户配额
   * @returns {Promise<object>} 配额信息
   */
  static async checkUserQuota() {
    try {
      const currentUserId = UserManager.getCurrentUserId()
      if (!currentUserId) {
        return {
          hasQuota: false,
          message: '用户未登录'
        }
      }

      // 导入API
      const { checkQuota } = await import('../api/user.js')
      
      // 调用后端接口检查配额
      const result = await checkQuota(currentUserId)
      
      if (result && result.success && result.data) {
        return {
          hasQuota: result.data.hasQuota,
          dailyUsage: result.data.dailyUsage,
          dailyQuota: result.data.dailyQuota,
          remainingQuota: result.data.remainingQuota,
          totalUsage: result.data.totalUsage,
          message: result.data.hasQuota ? '配额充足' : '今日配额已用完'
        }
      } else {
        return {
          hasQuota: false,
          message: result?.message || '检查配额失败'
        }
      }
    } catch (error) {
      console.error('检查用户配额异常:', error)
      return {
        hasQuota: false,
        message: '检查配额异常'
      }
    }
  }

  /**
   * 获取用户统计数据
   * @returns {Promise<object>} 统计数据
   */
  static async getUserStats() {
    try {
      const currentUserId = UserManager.getCurrentUserId()
      if (!currentUserId) {
        return null
      }

      // 导入API
      const { getUserStats } = await import('../api/user.js')
      
      // 调用后端接口获取统计数据
      const result = await getUserStats(currentUserId)
      
      if (result && result.success && result.data) {
        return result.data
      } else {
        console.warn('获取用户统计数据失败:', result?.message)
        return null
      }
    } catch (error) {
      console.error('获取用户统计数据异常:', error)
      return null
    }
  }

  /**
   * 使用前检查配额
   * @param {string} feature - 功能名称
   * @returns {Promise<boolean>} 是否可以使用
   */
  static async checkBeforeUse(feature = 'general') {
    const quotaInfo = await this.checkUserQuota()
    
    if (!quotaInfo.hasQuota) {
      // 显示配额不足提示
      const quotaDisplay = quotaInfo.isUnlimited || quotaInfo.dailyQuota === -1 ? '∞' : (quotaInfo.dailyQuota || 10)
      uni.showModal({
        title: '使用次数不足',
        content: quotaInfo.message + '\n\n今日已使用: ' + (quotaInfo.dailyUsage || 0) + '次\n今日配额: ' + quotaDisplay + '次',
        showCancel: false,
        confirmText: '知道了'
      })
      return false
    }
    
    return true
  }

  /**
   * 使用功能并记录统计
   * @param {string} feature - 功能名称
   * @param {Function} callback - 使用功能的回调函数
   * @returns {Promise<any>} 功能执行结果
   */
  static async useFeature(feature, callback) {
    // 使用前检查配额
    const canUse = await this.checkBeforeUse(feature)
    if (!canUse) {
      return null
    }

    try {
      // 执行功能
      const result = await callback()
      
      // 记录使用统计
      await this.recordUsage(feature)
      
      return result
    } catch (error) {
      console.error(`使用功能失败: feature=${feature}`, error)
      throw error
    }
  }
}

export default StatsManager
