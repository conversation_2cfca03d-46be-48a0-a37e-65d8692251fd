<template>
  <view class="container">
    <!-- 头部 -->
    <view class="header">
      <text class="title">VIP会员升级</text>
      <text class="subtitle">输入激活码升级为VIP会员</text>
    </view>

    <!-- VIP特权介绍 -->
    <view class="privileges-section">
      <text class="section-title">VIP特权</text>
      <view class="privilege-list">
        <view class="privilege-item">
          <view class="privilege-icon">🚀</view>
          <view class="privilege-content">
            <text class="privilege-title">使用次数提升</text>
            <text class="privilege-desc">每日使用次数从10次提升至100次</text>
          </view>
        </view>
        <view class="privilege-item">
          <view class="privilege-icon">⚡</view>
          <view class="privilege-content">
            <text class="privilege-title">优先处理</text>
            <text class="privilege-desc">请求优先处理，响应更快</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 激活码输入 -->
    <view class="activation-section">
      <text class="section-title">激活码升级</text>
      <view class="activation-form">
        <view class="input-group">
          <text class="input-label">请输入VIP激活码</text>
          <input
            class="activation-input"
            v-model="activationCode"
            placeholder="XXXX-XXXX-XXXX-XXXX"
            maxlength="19"
          />
        </view>
        <view class="tips">
          <text class="tips-text">• 激活码格式：XXXX-XXXX-XXXX-XXXX</text>
          <text class="tips-text">• 每个激活码只能使用一次</text>
          <text class="tips-text">• 激活后立即生效，有效期根据激活码类型确定</text>
        </view>
      </view>
    </view>

    <!-- 如何获取激活码 -->
    <view class="how-to-get-section">
      <text class="section-title">如何获取激活码？</text>
      <view class="method-list">
        <view class="method-item">
          <view class="method-icon">🎁</view>
          <view class="method-content">
            <text class="method-title">活动赠送</text>
            <text class="method-desc">参与官方活动获得免费激活码</text>
          </view>
        </view>
        <view class="method-item">
          <view class="method-icon">👥</view>
          <view class="method-content">
            <text class="method-title">邀请好友</text>
            <text class="method-desc">邀请好友注册可获得激活码奖励</text>
          </view>
        </view>
        <view class="method-item">
          <view class="method-icon">💬</view>
          <view class="method-content">
            <text class="method-title">联系客服</text>
            <text class="method-desc">联系客服咨询激活码购买方式</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 激活按钮 -->
    <view class="actions">
      <button class="activate-btn" @click="handleActivation" :disabled="!activationCode.trim()">
        激活VIP会员
      </button>
    </view>

    <!-- 服务条款 -->
    <view class="terms">
      <text class="terms-text">
        激活即表示同意
        <text class="terms-link" @click="showTerms">《VIP服务协议》</text>
        和
        <text class="terms-link" @click="showPrivacy">《隐私政策》</text>
      </text>
    </view>
  </view>
</template>

<script>
import { UserManager } from '../../utils/user.js'

export default {
  name: 'VipUpgrade',

  data() {
    return {
      activationCode: ''
    }
  },

  methods: {
    // 处理激活
    async handleActivation() {
      if (!this.activationCode.trim()) {
        uni.showToast({
          title: '请输入激活码',
          icon: 'none'
        })
        return
      }

      // 验证激活码格式
      if (!this.validateActivationCode(this.activationCode)) {
        uni.showToast({
          title: '激活码格式不正确',
          icon: 'none'
        })
        return
      }

      // 检查用户登录状态
      const currentUserId = UserManager.getCurrentUserId()
      const token = uni.getStorageSync('token')

      console.log('登录状态检查:', {
        userId: currentUserId,
        hasToken: !!token,
        tokenLength: token ? token.length : 0
      })

      if (!currentUserId || !token) {
        uni.showModal({
          title: '请先登录',
          content: '激活VIP需要先登录账户',
          showCancel: false,
          confirmText: '去登录',
          success: () => {
            uni.navigateTo({
              url: '/pages/login/login'
            })
          }
        })
        return
      }

      try {
        uni.showLoading({
          title: '正在激活...'
        })

        // 调用激活接口
        await this.activateVip(this.activationCode)

      } catch (error) {
        uni.hideLoading()
        console.error('激活失败:', error)
        uni.showModal({
          title: '激活失败',
          content: error.message || '激活码无效或已被使用',
          showCancel: false
        })
      }
    },

    // 验证激活码格式
    validateActivationCode(code) {
      if (!code) return false

      // 严格验证格式：XXXX-XXXX-XXXX-XXXX
      const formatRegex = /^[A-Za-z0-9]{4}-[A-Za-z0-9]{4}-[A-Za-z0-9]{4}-[A-Za-z0-9]{4}$/
      return formatRegex.test(code)
    },

    // 激活VIP
    async activateVip(code) {
      try {
        // 直接使用用户输入的激活码，不做任何处理

        // 调用后端API验证激活码
        const { activateVip } = await import('../../api/user.js')
        const currentUserId = UserManager.getCurrentUserId()

        console.log('激活VIP请求参数:', {
          userId: currentUserId,
          activationCode: code.trim(),
          token: uni.getStorageSync('token')
        })

        const result = await activateVip(currentUserId, code.trim())

        uni.hideLoading()

        if (result && result.success) {
          // 更新本地用户信息
          UserManager.updateUserField('isVip', 1)
          UserManager.updateUserField('vipExpireTime', result.data.expireTime)

          uni.showModal({
            title: '激活成功',
            content: `恭喜您成功升级为VIP会员！\n有效期至：${this.formatDate(result.data.expireTime)}`,
            showCancel: false,
            confirmText: '确定',
            success: () => {
              // 返回上一页
              uni.navigateBack()
            }
          })
        } else {
          throw new Error(result?.message || '激活失败')
        }
      } catch (error) {
        console.error('激活VIP失败:', error)
        uni.hideLoading()
        throw error
      }
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    },

    // 显示服务条款
    showTerms() {
      uni.showModal({
        title: 'VIP服务协议',
        content: '这里是VIP服务协议的内容...',
        showCancel: false
      })
    },

    // 显示隐私政策
    showPrivacy() {
      uni.showModal({
        title: '隐私政策',
        content: '这里是隐私政策的内容...',
        showCancel: false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.header {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  padding: 60rpx 30rpx 40rpx;
  text-align: center;
  color: white;
  
  .title {
    display: block;
    font-size: 40rpx;
    font-weight: bold;
    margin-bottom: 10rpx;
  }
  
  .subtitle {
    font-size: 28rpx;
    opacity: 0.9;
  }
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.privileges-section {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  
  .privilege-list {
    .privilege-item {
      display: flex;
      align-items: center;
      padding: 20rpx 0;
      border-bottom: 1rpx solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      .privilege-icon {
        font-size: 40rpx;
        margin-right: 20rpx;
      }
      
      .privilege-content {
        flex: 1;
        
        .privilege-title {
          display: block;
          font-size: 28rpx;
          font-weight: bold;
          color: #333;
          margin-bottom: 5rpx;
        }
        
        .privilege-desc {
          font-size: 24rpx;
          color: #666;
        }
      }
    }
  }
}

.activation-section {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;

  .activation-form {
    .input-group {
      margin-bottom: 30rpx;

      .input-label {
        display: block;
        font-size: 28rpx;
        color: #333;
        margin-bottom: 15rpx;
        font-weight: 500;
      }

      .activation-input {
        width: 100%;
        height: 80rpx;
        padding: 0 20rpx;
        border: 2rpx solid #e0e0e0;
        border-radius: 12rpx;
        font-size: 32rpx;
        text-align: center;
        letter-spacing: 4rpx;
        font-family: 'Courier New', monospace;
        font-weight: bold;

        &:focus {
          border-color: #ff6b35;
          box-shadow: 0 0 0 2rpx rgba(255, 107, 53, 0.2);
        }

        &::placeholder {
          color: #ccc;
          font-weight: normal;
        }
      }
    }

    .tips {
      .tips-text {
        display: block;
        font-size: 24rpx;
        color: #666;
        margin-bottom: 8rpx;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

.how-to-get-section {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;

  .method-list {
    .method-item {
      display: flex;
      align-items: center;
      padding: 20rpx 0;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .method-icon {
        font-size: 40rpx;
        margin-right: 20rpx;
      }

      .method-content {
        flex: 1;

        .method-title {
          display: block;
          font-size: 28rpx;
          font-weight: bold;
          color: #333;
          margin-bottom: 5rpx;
        }

        .method-desc {
          font-size: 24rpx;
          color: #666;
        }
      }
    }
  }
}

.actions {
  padding: 40rpx 30rpx;

  .activate-btn {
    width: 100%;
    height: 88rpx;
    background: #ff6b35;
    color: white;
    border: none;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: bold;

    &:disabled {
      background: #ccc;
    }
  }
}

.terms {
  padding: 0 30rpx 40rpx;
  text-align: center;
  
  .terms-text {
    font-size: 24rpx;
    color: #999;
    
    .terms-link {
      color: #ff6b35;
    }
  }
}
</style>
