package com.emotional.service.service.impl;

import com.emotional.service.dto.EmotionAnalyzeRequest;
import com.emotional.service.dto.EmotionAnalyzeResponse;
import com.emotional.service.entity.ReplyHistory;
import com.emotional.service.service.EmotionService;
import com.emotional.service.service.ReplyHistoryService;
import com.emotional.service.service.UserSettingsService;
// 移除本地算法依赖，专门使用DeepSeek AI服务
import com.emotional.service.llm.impl.DeepSeekService;
import com.emotional.service.dto.llm.EmotionAnalysisRequest;
import com.emotional.service.dto.llm.EmotionAnalysisResponse;
import com.emotional.service.dto.llm.ReplyGenerationRequest;
import com.emotional.service.dto.llm.ReplyGenerationResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 情感分析服务实现
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EmotionServiceImpl implements EmotionService {

    private final ReplyHistoryService replyHistoryService;
    private final DeepSeekService deepSeekService;
    private final UserSettingsService userSettingsService;
    
    @Override
    public EmotionAnalyzeResponse analyzeAndGenerateReply(EmotionAnalyzeRequest request, 
                                                         String clientIp, 
                                                         String userAgent) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 1. 情感分析
            log.info("开始情感分析: {}", request.getMessage());
            EmotionAnalyzeResponse.EmotionResult emotionResult = analyzeEmotion(request.getMessage());
            
            // 2. 根据用户设置智能选择回复风格
            List<String> finalReplyStyles = determineReplyStyles(request);
            log.info("开始生成回复，检测到情感: {}，使用风格: {}", emotionResult.getEmotion(), finalReplyStyles);

            List<EmotionAnalyzeResponse.ReplyOption> replyOptions = generateReplies(
                request.getMessage(), emotionResult, finalReplyStyles);
            
            // 3. 保存历史记录
            Long historyId = null;
            if (request.getSaveHistory() && request.getUserId() != null && request.getUserId() > 0) {
                historyId = saveReplyHistory(request, emotionResult, replyOptions,
                                           clientIp, userAgent, startTime);
            } else if (request.getSaveHistory()) {
                log.warn("用户ID无效，跳过历史记录保存: userId={}", request.getUserId());
            }
            
            // 4. 构建响应
            EmotionAnalyzeResponse response = new EmotionAnalyzeResponse();
            response.setHistoryId(historyId);
            response.setOriginalMessage(request.getMessage());
            response.setEmotionResult(emotionResult);
            response.setReplyOptions(replyOptions);
            response.setProcessTime(System.currentTimeMillis() - startTime);
            
            log.info("情感分析和回复生成完成，耗时: {}ms", response.getProcessTime());
            return response;
            
        } catch (Exception e) {
            log.error("情感分析和回复生成失败", e);
            throw new RuntimeException("处理失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public EmotionAnalyzeResponse regenerateReply(Long historyId, EmotionAnalyzeRequest request) {
        // 获取历史记录
        ReplyHistory history = replyHistoryService.getById(historyId);
        if (history == null) {
            throw new RuntimeException("历史记录不存在");
        }
        
        // 使用历史记录中的消息重新生成
        EmotionAnalyzeRequest newRequest = new EmotionAnalyzeRequest();
        newRequest.setMessage(history.getOriginalMessage());
        newRequest.setReplyStyles(request != null ? request.getReplyStyles() : null);
        newRequest.setSaveHistory(false); // 不保存新的历史记录
        
        return analyzeAndGenerateReply(newRequest, null, null);
    }
    
    @Override
    public Map<String, String> getReplyStyles() {
        Map<String, String> styles = new LinkedHashMap<>();
        styles.put("warm_caring", "暖男");
        styles.put("humorous", "玩梗");
        styles.put("romantic", "撩妹");
        styles.put("high_eq", "高情商");
        styles.put("direct", "直接");
        styles.put("mature", "成熟稳重");
        styles.put("gentle", "温柔大叔");
        styles.put("dominant", "霸道总裁");
        styles.put("literary", "文艺风格");
        styles.put("detailed", "话痨风格");
        return styles;
    }

    /**
     * 根据用户设置和情感分析结果智能确定回复风格
     */
    private List<String> determineReplyStyles(EmotionAnalyzeRequest request) {
        Long userId = request.getUserId();

        // 如果请求中指定了风格，直接使用
        if (request.getReplyStyles() != null && !request.getReplyStyles().isEmpty()) {
            return request.getReplyStyles();
        }

        // 如果没有用户ID，使用默认风格
        if (userId == null || userId <= 0) {
            return Arrays.asList("warm_caring", "humorous", "high_eq");
        }

        try {
            // 获取用户设置
            String generationMode = userSettingsService.getUserReplyGenerationMode(userId);
            Integer preferredCount = userSettingsService.getUserPreferredReplyCount(userId);

            List<String> selectedStyles;

            switch (generationMode) {
                case "single":
                    // 单一模式：只使用主要风格
                    String primaryStyle = userSettingsService.getUserPrimaryStyle(userId);
                    selectedStyles = Arrays.asList(primaryStyle);
                    break;

                case "custom":
                    // 自定义模式：使用用户设置的风格列表
                    List<String> userStyles = userSettingsService.getUserReplyStyles(userId);
                    selectedStyles = userStyles.subList(0, Math.min(userStyles.size(), preferredCount));
                    break;

                case "smart":
                default:
                    // 智能模式：根据情感分析结果选择合适的风格
                    selectedStyles = getSmartReplyStyles(request.getMessage(), preferredCount);
                    break;
            }

            log.info("用户{}使用{}模式，生成{}个风格: {}", userId, generationMode, selectedStyles.size(), selectedStyles);
            return selectedStyles;

        } catch (Exception e) {
            log.error("确定回复风格失败，使用默认风格: userId={}", userId, e);
            // 使用默认风格，但要遵循用户设置的数量
            List<String> defaultStyles = Arrays.asList("warm_caring", "humorous", "high_eq");
            Integer userPreferredCount = null;
            try {
                userPreferredCount = userSettingsService.getUserPreferredReplyCount(userId);
            } catch (Exception ex) {
                log.warn("获取用户首选数量失败，使用默认值: userId={}", userId);
            }
            int count = userPreferredCount != null ? userPreferredCount : 2;
            return defaultStyles.subList(0, Math.min(defaultStyles.size(), count));
        }
    }

    /**
     * 智能模式：根据消息内容选择合适的回复风格
     */
    private List<String> getSmartReplyStyles(String message, Integer count) {
        // 确保count不为null，默认为2
        if (count == null || count <= 0) {
            count = 2;
        }

        // 简单的关键词匹配，实际可以结合情感分析结果
        Map<String, List<String>> emotionStyleMap = new HashMap<>();
        emotionStyleMap.put("难过", Arrays.asList("warm_caring", "gentle", "high_eq"));
        emotionStyleMap.put("开心", Arrays.asList("humorous", "romantic", "warm_caring"));
        emotionStyleMap.put("愤怒", Arrays.asList("mature", "high_eq", "gentle"));
        emotionStyleMap.put("担心", Arrays.asList("warm_caring", "mature", "high_eq"));
        emotionStyleMap.put("兴奋", Arrays.asList("humorous", "romantic", "warm_caring"));
        emotionStyleMap.put("平静", Arrays.asList("mature", "literary", "gentle"));
        emotionStyleMap.put("关心", Arrays.asList("warm_caring", "romantic", "gentle"));
        emotionStyleMap.put("感谢", Arrays.asList("warm_caring", "high_eq", "mature"));

        // 默认风格组合
        List<String> defaultStyles = Arrays.asList("warm_caring", "humorous", "high_eq");

        // 检查消息中是否包含情感关键词
        for (Map.Entry<String, List<String>> entry : emotionStyleMap.entrySet()) {
            if (message.contains(entry.getKey())) {
                List<String> styles = entry.getValue();
                return styles.subList(0, Math.min(styles.size(), count));
            }
        }

        return defaultStyles.subList(0, Math.min(defaultStyles.size(), count));
    }
    
    @Override
    public EmotionAnalyzeResponse.EmotionResult analyzeEmotion(String message) {
        // 检查DeepSeek服务是否可用
        if (!deepSeekService.isAvailable()) {
            throw new RuntimeException("DeepSeek服务不可用，请联系管理员配置API密钥");
        }

        try {
            EmotionAnalysisRequest llmRequest = EmotionAnalysisRequest.builder()
                    .message(message)
                    .language("zh-CN")
                    .detailed(true)
                    .temperature(0.3)
                    .build();

            EmotionAnalysisResponse llmResponse = deepSeekService.analyzeEmotion(llmRequest);

            if (llmResponse.getSuccess()) {
                // 转换DeepSeek响应为现有格式
                EmotionAnalyzeResponse.EmotionResult result = new EmotionAnalyzeResponse.EmotionResult();
                result.setEmotion(llmResponse.getPrimaryEmotion());
                result.setConfidence(llmResponse.getConfidence());
                result.setIntensity(llmResponse.getIntensity());

                log.info("使用DeepSeek情感分析成功: 情感={}, 置信度={}",
                        result.getEmotion(), result.getConfidence());
                return result;
            } else {
                // 抛出具体的错误信息
                throw new RuntimeException(llmResponse.getErrorMessage());
            }
        } catch (Exception e) {
            log.error("DeepSeek情感分析失败", e);
            throw new RuntimeException(e.getMessage(), e);
        }
    }
    
    @Override
    public List<EmotionAnalyzeResponse.ReplyOption> generateReplies(String message,
                                                                   EmotionAnalyzeResponse.EmotionResult emotionResult,
                                                                   List<String> replyStyles) {
        // 如果没有指定风格，使用默认风格
        if (replyStyles == null || replyStyles.isEmpty()) {
            replyStyles = Arrays.asList("warm_caring", "humorous", "rational", "concise", "romantic");
        }

        // 检查DeepSeek服务是否可用
        if (!deepSeekService.isAvailable()) {
            throw new RuntimeException("DeepSeek服务不可用，请联系管理员配置API密钥");
        }

        try {
            // 构建DeepSeek情感分析响应
            EmotionAnalysisResponse llmEmotionResponse = EmotionAnalysisResponse.builder()
                    .primaryEmotion(emotionResult.getEmotion())
                    .confidence(emotionResult.getConfidence())
                    .intensity(emotionResult.getIntensity())
                    .build();

            // 使用传入的风格列表大小作为生成数量，这样就能遵循用户设置的数量
            int replyCount = replyStyles.size();

            ReplyGenerationRequest llmRequest = ReplyGenerationRequest.builder()
                    .originalMessage(message)
                    .emotionAnalysis(llmEmotionResponse)
                    .replyStyles(replyStyles)
                    .count(replyCount)
                    .maxLength(200)
                    .minLength(10)
                    .temperature(0.7)
                    .language("zh-CN")
                    .build();

            ReplyGenerationResponse llmResponse = deepSeekService.generateReply(llmRequest);

            if (llmResponse.getSuccess() && llmResponse.getReplies() != null && !llmResponse.getReplies().isEmpty()) {
                // 转换DeepSeek响应为现有格式
                List<EmotionAnalyzeResponse.ReplyOption> replyOptions = new ArrayList<>();

                for (ReplyGenerationResponse.GeneratedReply generatedReply : llmResponse.getReplies()) {
                    EmotionAnalyzeResponse.ReplyOption option = new EmotionAnalyzeResponse.ReplyOption();
                    option.setContent(generatedReply.getContent());
                    option.setStyle(generatedReply.getStyle() != null ? generatedReply.getStyle() : "general");
                    option.setStyleName(generatedReply.getStyleName() != null ? generatedReply.getStyleName() : "通用");

                    // 设置推荐度和置信度
                    if (generatedReply.getRecommendationScore() != null) {
                        option.setRecommendation(generatedReply.getRecommendationScore());
                    } else {
                        option.setRecommendation(3); // 默认推荐度
                    }

                    if (generatedReply.getQualityScore() != null) {
                        option.setConfidence(generatedReply.getQualityScore());
                    } else {
                        option.setConfidence(0.8); // 默认置信度
                    }

                    replyOptions.add(option);
                }

                log.info("使用DeepSeek生成回复成功: 生成{}个回复", replyOptions.size());
                return replyOptions;
            } else {
                // 抛出具体的错误信息
                throw new RuntimeException(llmResponse.getErrorMessage());
            }
        } catch (Exception e) {
            log.error("DeepSeek回复生成失败", e);
            throw new RuntimeException(e.getMessage(), e);
        }
    }
    
    /**
     * 保存回复历史记录
     */
    private Long saveReplyHistory(EmotionAnalyzeRequest request,
                                 EmotionAnalyzeResponse.EmotionResult emotionResult,
                                 List<EmotionAnalyzeResponse.ReplyOption> replyOptions,
                                 String clientIp,
                                 String userAgent,
                                 long startTime) {
        try {
            // 检查用户ID是否有效
            Long userId = request.getUserId();
            if (userId == null || userId <= 0) {
                log.warn("用户ID无效，跳过历史记录保存: userId={}", userId);
                return null;
            }

            ReplyHistory history = new ReplyHistory();
            history.setUserId(userId);
            history.setOriginalMessage(request.getMessage());
            history.setEmotionResult(emotionResult.getEmotion());
            history.setEmotionConfidence(emotionResult.getConfidence());
            
            // 将回复列表转换为JSON字符串保存
            history.setReplyList(convertReplyOptionsToJson(replyOptions));
            
            history.setIsFavorite(0);
            history.setProcessTime(System.currentTimeMillis() - startTime);
            history.setClientIp(clientIp);
            history.setUserAgent(userAgent);
            
            replyHistoryService.save(history);
            return history.getId();
            
        } catch (Exception e) {
            log.error("保存历史记录失败", e);
            // 不抛出异常，避免影响主流程
            return null;
        }
    }
    
    /**
     * 将回复选项转换为JSON字符串
     */
    private String convertReplyOptionsToJson(List<EmotionAnalyzeResponse.ReplyOption> replyOptions) {
        // 这里可以使用Jackson或FastJSON进行序列化
        // 暂时返回简单的字符串表示
        StringBuilder sb = new StringBuilder();
        sb.append("[");
        for (int i = 0; i < replyOptions.size(); i++) {
            if (i > 0) sb.append(",");
            EmotionAnalyzeResponse.ReplyOption option = replyOptions.get(i);
            sb.append("{")
              .append("\"content\":\"").append(option.getContent()).append("\",")
              .append("\"style\":\"").append(option.getStyle()).append("\",")
              .append("\"styleName\":\"").append(option.getStyleName()).append("\"")
              .append("}");
        }
        sb.append("]");
        return sb.toString();
    }
}
