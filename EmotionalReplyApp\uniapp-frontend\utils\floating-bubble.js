/**
 * 悬浮气泡工具类
 * 封装悬浮气泡的控制逻辑
 */

class FloatingBubbleManager {
  constructor() {
    this.plugin = null
    this.isInitialized = false
    this.init()
  }

  /**
   * 初始化插件
   */
  init() {
    // #ifdef APP-PLUS
    try {
      this.plugin = uni.requireNativePlugin('floating-window-plugin')
      this.isInitialized = true
      console.log('✅ 悬浮气泡插件初始化成功')
    } catch (error) {
      console.error('❌ 悬浮气泡插件初始化失败:', error)
      this.isInitialized = false
    }
    // #endif
    
    // #ifndef APP-PLUS
    console.log('⚠️ 悬浮气泡功能仅在App环境中可用')
    this.isInitialized = false
    // #endif
  }

  /**
   * 检查是否支持悬浮气泡
   */
  isSupported() {
    return this.isInitialized && this.plugin !== null
  }

  /**
   * 检查悬浮窗权限
   */
  async checkPermission() {
    return new Promise((resolve) => {
      if (!this.isSupported()) {
        resolve({ hasPermission: false, error: '插件未初始化' })
        return
      }

      this.plugin.hasOverlayPermission((result) => {
        console.log('🔍 悬浮窗权限检查:', result)
        resolve(result)
      })
    })
  }

  /**
   * 申请悬浮窗权限
   */
  async requestPermission() {
    return new Promise((resolve) => {
      if (!this.isSupported()) {
        resolve({ granted: false, error: '插件未初始化' })
        return
      }

      this.plugin.requestOverlayPermission((result) => {
        console.log('📋 悬浮窗权限申请结果:', result)
        resolve(result)
      })
    })
  }

  /**
   * 显示悬浮气泡
   */
  async showBubble(options = {}) {
    return new Promise(async (resolve) => {
      if (!this.isSupported()) {
        resolve({ success: false, message: '插件未初始化' })
        return
      }

      // 检查权限
      const permissionResult = await this.checkPermission()
      if (!permissionResult.hasPermission) {
        // 尝试申请权限
        const requestResult = await this.requestPermission()
        if (!requestResult.granted) {
          resolve({ 
            success: false, 
            message: '悬浮窗权限被拒绝',
            needPermission: true 
          })
          return
        }
      }

      // 默认配置
      const defaultOptions = {
        x: 100,
        y: 200,
        size: 56,
        alpha: 0.8
      }

      const finalOptions = { ...defaultOptions, ...options }

      this.plugin.showFloatingBubble(finalOptions, (result) => {
        console.log('🎈 显示悬浮气泡结果:', result)
        resolve(result)
      })
    })
  }

  /**
   * 隐藏悬浮气泡
   */
  async hideBubble() {
    return new Promise((resolve) => {
      if (!this.isSupported()) {
        resolve({ success: false, message: '插件未初始化' })
        return
      }

      this.plugin.hideFloatingBubble((result) => {
        console.log('🫥 隐藏悬浮气泡结果:', result)
        resolve(result)
      })
    })
  }

  /**
   * 检查悬浮气泡是否正在显示
   */
  async isBubbleShowing() {
    return new Promise((resolve) => {
      if (!this.isSupported()) {
        resolve({ isShowing: false, error: '插件未初始化' })
        return
      }

      this.plugin.isBubbleShowing((result) => {
        console.log('🔍 悬浮气泡状态:', result)
        resolve(result)
      })
    })
  }

  /**
   * 更新悬浮气泡配置
   */
  async updateBubbleConfig(config) {
    return new Promise((resolve) => {
      if (!this.isSupported()) {
        resolve({ success: false, message: '插件未初始化' })
        return
      }

      this.plugin.updateBubbleConfig(config, (result) => {
        console.log('⚙️ 更新悬浮气泡配置结果:', result)
        resolve(result)
      })
    })
  }

  /**
   * 切换悬浮气泡显示状态
   */
  async toggleBubble(options = {}) {
    const statusResult = await this.isBubbleShowing()
    
    if (statusResult.isShowing) {
      return await this.hideBubble()
    } else {
      return await this.showBubble(options)
    }
  }

  /**
   * 获取调试信息
   */
  getDebugInfo() {
    return {
      isSupported: this.isSupported(),
      isInitialized: this.isInitialized,
      hasPlugin: this.plugin !== null,
      platform: uni.getSystemInfoSync().platform
    }
  }
}

// 创建全局实例
const floatingBubbleManager = new FloatingBubbleManager()

export { FloatingBubbleManager, floatingBubbleManager }
export default floatingBubbleManager
