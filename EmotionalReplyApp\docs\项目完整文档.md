# 情感回复助手 - 项目完整文档

## 📋 **项目概述**

情感回复助手是一个基于AI的智能回复生成系统，帮助用户根据不同情感和场景生成个性化的回复内容。

### **核心功能**
- 🎭 **智能回复生成**：10种不同风格的个性化回复
- 👤 **用户管理**：普通用户、VIP用户、管理员三级权限
- 🎫 **激活码系统**：6种时长的VIP激活码（1天-1年）
- 📊 **配额管理**：普通用户5次/天，VIP用户50次/天，管理员无限制
- ⚙️ **个性化设置**：用户可自定义回复风格和生成模式

## 🗄️ **数据库设计**

### **核心表结构**

#### **1. 用户表 (users)**
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    nickname VARCHAR(100),
    email VARCHAR(100) UNIQUE,
    password VARCHAR(255) NOT NULL,
    daily_quota INT DEFAULT 5,
    used_quota INT DEFAULT 0,
    quota_reset_time DATETIME,
    is_vip TINYINT DEFAULT 0,
    vip_expire_time DATETIME,
    is_admin TINYINT DEFAULT 0,
    status TINYINT DEFAULT 0,
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### **2. 回复历史表 (reply_history)**
```sql
CREATE TABLE reply_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    original_message TEXT NOT NULL,
    emotion_result VARCHAR(50),
    emotion_confidence DECIMAL(5,2),
    reply_list JSON NOT NULL,
    selected_reply TEXT,
    selected_style VARCHAR(50),
    is_favorite TINYINT DEFAULT 0,
    process_time INT,
    client_ip VARCHAR(45),
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### **3. 用户设置表 (user_settings)**
```sql
CREATE TABLE user_settings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT UNIQUE NOT NULL,
    theme VARCHAR(20) DEFAULT 'light',
    font_size VARCHAR(20) DEFAULT 'medium',
    auto_save TINYINT DEFAULT 1,
    reply_styles TEXT,
    preferred_reply_count INT DEFAULT 2,
    reply_generation_mode VARCHAR(20) DEFAULT 'smart',
    primary_style VARCHAR(50) DEFAULT 'warm_caring',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### **4. 激活码表 (activation_codes)**
```sql
CREATE TABLE activation_codes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(32) UNIQUE NOT NULL,
    code_type VARCHAR(20) NOT NULL,
    duration_days INT NOT NULL,
    batch_id VARCHAR(64),
    created_by BIGINT NOT NULL,
    used_by BIGINT,
    used_time DATETIME,
    status TINYINT DEFAULT 0,
    remark VARCHAR(255),
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### **5. 系统配置表 (system_config)**
```sql
CREATE TABLE system_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT NOT NULL,
    config_desc VARCHAR(255),
    config_type VARCHAR(20) DEFAULT 'string',
    is_public TINYINT DEFAULT 0,
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 🎭 **回复风格系统**

### **10种回复风格**
| 风格代码 | 显示名称 | 特点描述 |
|---------|----------|----------|
| warm_caring | 暖男 | 温暖体贴，关怀备至 |
| humorous | 逗比 | 幽默搞笑，轻松愉快 |
| romantic | 撩妹 | 浪漫甜蜜，情话连篇 |
| high_eq | 高情商 | 智慧回应，情商很高 |
| direct | 直接 | 简洁明了，直截了当 |
| mature | 成熟稳重 | 理性冷静，可靠踏实 |
| gentle | 温柔大叔 | 成熟温和，包容理解 |
| dominant | 霸道总裁 | 强势自信，领导风范 |
| literary | 发表文学 | 文艺范儿，有深度 |
| detailed | 话痨延申 | 详细展开，深入交流 |

### **三种生成模式**

#### **🧠 智能模式 (smart)**
- 系统根据消息情感自动选择合适风格
- 适合不想复杂设置的用户
- 默认生成2个回复

#### **🎯 自定义模式 (custom)**
- 用户选择1-3种喜欢的风格组合
- 可设置生成数量（1-3个）
- 完全个性化的体验

#### **⚡ 单一模式 (single)**
- 只使用一种主要风格
- 最节省API调用，每次只生成1个回复
- 适合有明确偏好的用户

## 🎫 **激活码系统**

### **激活码类型**
| 类型代码 | 显示名称 | 有效天数 | 使用场景 |
|---------|----------|----------|----------|
| vip_1d | 1天VIP | 1天 | 新用户体验，免费试用 |
| vip_7d | 7天VIP | 7天 | 短期试用，活动推广 |
| vip_1m | 1个月VIP | 30天 | 标准月度会员 |
| vip_3m | 3个月VIP | 90天 | 季度优惠套餐 |
| vip_6m | 6个月VIP | 180天 | 半年优惠套餐 |
| vip_1y | 1年VIP | 365天 | 年度最优惠套餐 |

### **激活码特性**
- **永久有效**：激活码本身不会过期
- **批次管理**：支持批量生成和管理
- **状态简单**：只有未使用(0)和已使用(1)两种状态
- **使用记录**：完整的使用历史追踪

## 👥 **用户权限系统**

### **用户类型**
| 用户类型 | 每日配额 | 特殊权限 | 说明 |
|---------|----------|----------|------|
| 普通用户 | 5次/天 | 基础功能 | 免费用户 |
| VIP用户 | 50次/天 | 高级功能 | 付费用户 |
| 管理员 | 无限制 | 全部权限 | 系统管理 |

### **管理员功能**
- 🎫 **激活码管理**：生成、查看、禁用激活码
- 👤 **用户管理**：查看用户信息、修改权限
- 📊 **数据统计**：使用情况、系统监控
- ⚙️ **系统配置**：修改系统参数

## 🔧 **技术架构**

### **后端技术栈**
- **框架**：Spring Boot 2.7+
- **数据库**：MySQL 8.0+
- **ORM**：MyBatis-Plus
- **缓存**：Redis
- **AI服务**：DeepSeek API
- **邮件服务**：Spring Mail (支持QQ邮箱、163邮箱等)
- **安全认证**：JWT Token

### **前端技术栈**
- **框架**：uni-app (Vue 3)
- **语言**：JavaScript/TypeScript
- **UI组件**：uni-ui + 自定义组件
- **状态管理**：Vuex
- **网络请求**：封装的request工具
- **支持平台**：Android、iOS、H5、微信小程序

### **项目结构**
```
EmotionalReplyApp/
├── backend-service/         # Spring Boot后端服务
│   ├── src/main/java/      # Java源码
│   ├── src/main/resources/ # 配置文件和SQL脚本
│   └── pom.xml            # Maven依赖配置
├── uniapp-frontend/        # uni-app前端应用
│   ├── pages/             # 页面文件
│   ├── components/        # 组件文件
│   ├── api/              # API接口
│   ├── store/            # 状态管理
│   └── utils/            # 工具函数
├── deploy/                # 部署脚本和配置
├── docs/                 # 项目文档
└── native-plugins/       # 原生插件
```

### **核心API接口**

#### **情感分析与回复生成**
```
POST /emotion/analyze
{
  "message": "今天心情不太好",
  "userId": 1,
  "replyStyles": ["warm_caring", "humorous"],
  "saveHistory": true
}
```

#### **用户设置管理**
```
GET /user/settings/{userId}/reply-styles
PUT /user/settings/{userId}/reply-styles
```

#### **激活码管理**
```
POST /activation/generate
POST /activation/use
GET /activation/list
```

## 📱 **用户使用指南**

### **回复风格设置流程**
1. **进入设置** → 点击底部"设置"
2. **选择模式** → 点击"🎭 回复风格"选择生成模式
3. **详细配置** → 如果选择自定义，会跳转到风格设置页面
4. **保存设置** → 设置会自动保存到服务器和本地

### **激活码使用流程**
1. **获取激活码** → 从管理员或活动获得
2. **进入激活页面** → 设置 → 升级VIP
3. **输入激活码** → 格式：XXXX-XXXX-XXXX-XXXX
4. **激活成功** → VIP权限立即生效

## 🚀 **部署指南**

### **环境要求**
- **Java**: JDK 8+
- **Node.js**: 14+
- **MySQL**: 8.0+
- **Redis**: 6.0+ (可选)

### **数据库部署**
```bash
# 1. 创建数据库
mysql -u root -p
CREATE DATABASE emotional_reply CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 2. 执行部署脚本
mysql -u root -p emotional_reply < backend-service/src/main/resources/sql/deploy.sql
```

### **后端部署**

#### **开发环境**
```bash
# 1. 配置环境变量
export MYSQL_URL=*******************************************
export MYSQL_USERNAME=root
export MYSQL_PASSWORD=your_password
export DEEPSEEK_API_KEY=your_deepseek_key

# 2. 启动服务
cd backend-service
mvn spring-boot:run
```

#### **生产环境**
```bash
# 1. 编译打包
mvn clean package -DskipTests

# 2. 运行服务
java -jar target/emotional-reply-service.jar

# 3. 使用systemd管理服务
sudo cp deploy/emotional-reply.service /etc/systemd/system/
sudo systemctl enable emotional-reply
sudo systemctl start emotional-reply
```

### **前端部署**

#### **H5版本部署**
```bash
# 1. 安装依赖
cd uniapp-frontend
npm install

# 2. 编译构建
npm run build:h5

# 3. 部署到Nginx
sudo cp -r dist/* /var/www/html/
```

#### **App版本发布**
```bash
# 1. 编译App
npm run build:app-android

# 2. 使用HBuilderX打包
# 在HBuilderX中选择"发行" -> "原生App-云打包"
```

### **邮件服务配置**
```bash
# 环境变量配置
export MAIL_HOST=smtp.qq.com
export MAIL_PORT=587
export MAIL_USERNAME=<EMAIL>
export MAIL_PASSWORD=your_authorization_code
```

### **Docker部署 (推荐)**
```bash
# 使用docker-compose一键部署
cd deploy
docker-compose up -d
```

## 📊 **性能优化效果**

### **回复风格优化**
- **API调用减少40-80%**：不再固定生成5个回复
- **用户体验提升**：得到符合偏好的回复风格
- **响应速度提升**：减少不必要的生成时间

### **激活码优化**
- **管理简化**：激活码永久有效，不需要处理过期逻辑
- **类型丰富**：6种时长满足不同用户需求
- **性能提升**：减少了数据库字段和查询复杂度

## 🎯 **测试账号**

| 用户类型 | 用户名 | 密码 | 每日配额 | 说明 |
|---------|--------|------|----------|------|
| 管理员 | admin | 123456 | 无限制 | 系统管理员 |
| VIP用户 | vipuser | 123456 | 50次/天 | VIP测试账号 |
| 普通用户 | testuser | 123456 | 5次/天 | 普通测试账号 |

### **测试激活码**
- `VIP1D-2024-ABCD-EFGH` - 1天VIP
- `VIP7D-2024-IJKL-MNOP` - 7天VIP
- `VIP1M-2024-QRST-UVWX` - 1个月VIP
- `VIP3M-2024-YZAB-CDEF` - 3个月VIP
- `VIP6M-2024-GHIJ-KLMN` - 6个月VIP
- `VIP1Y-2024-OPQR-STUV` - 1年VIP

## 🔍 **常见问题**

### **Q: 如何修改用户配额？**
A: 管理员可以直接修改数据库users表的daily_quota字段，或通过管理界面操作。

### **Q: 激活码使用失败怎么办？**
A: 检查激活码格式是否正确，确认激活码未被使用，联系管理员确认激活码有效性。

### **Q: 如何添加新的回复风格？**
A: 修改system_config表中的supported_reply_styles配置，同时更新前端的风格映射。

### **Q: 数据库如何备份？**
A: 定期执行 `mysqldump -u root -p emotional_reply > backup.sql` 进行备份。

### **Q: 如何配置邮件服务？**
A: 设置环境变量MAIL_HOST、MAIL_PORT、MAIL_USERNAME、MAIL_PASSWORD，支持QQ邮箱、163邮箱等。

### **Q: 前端如何切换API地址？**
A: 修改 `uniapp-frontend/api/config.js` 中的 `BASE_URL` 配置。

## 📚 **API接口文档**

### **用户相关接口**
```
POST /user/login          # 用户登录
POST /user/register       # 用户注册
GET  /user/info/{userId}  # 获取用户信息
PUT  /user/profile/{userId} # 更新用户资料
PUT  /user/password/{userId} # 修改密码
```

### **情感分析接口**
```
POST /emotion/analyze     # 情感分析和回复生成
GET  /emotion/styles      # 获取回复风格列表
```

### **激活码接口**
```
POST /activation/generate # 生成激活码(管理员)
POST /activation/use      # 使用激活码
GET  /activation/list     # 查看激活码列表(管理员)
```

### **用户设置接口**
```
GET  /user/settings/{userId}              # 获取用户设置
PUT  /user/settings/{userId}              # 更新用户设置
GET  /user/settings/{userId}/reply-styles # 获取回复风格偏好
PUT  /user/settings/{userId}/reply-styles # 更新回复风格偏好
```

### **历史记录接口**
```
GET  /history/{userId}           # 获取历史记录
POST /history/{userId}/favorite  # 收藏回复
DELETE /history/{historyId}      # 删除历史记录
```

---

**项目版本**：v1.0.0
**最后更新**：2024-01-15
**维护团队**：YUMU开发团队
**技术支持**：[GitHub Issues](https://github.com/your-repo/issues)
