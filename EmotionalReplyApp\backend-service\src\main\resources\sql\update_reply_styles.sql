-- =====================================================
-- 回复风格名称更新脚本
-- 用于将现有数据库中的风格名称更新为新的名称
-- =====================================================

USE emotional_reply_db;

-- 1. 更新 system_config 表中的回复风格配置
UPDATE system_config 
SET config_value = '{"warm_caring": "暖男", "humorous": "玩梗", "romantic": "撩妹", "high_eq": "高情商", "direct": "直接", "mature": "成熟稳重", "gentle": "温柔大叔", "dominant": "霸道总裁", "literary": "文艺风格", "detailed": "话痨风格"}'
WHERE config_key = 'supported_reply_styles';

-- 2. 更新 user_settings 表中用户的回复风格偏好
-- 如果用户设置中包含旧的风格名称，需要更新为新的风格代码
-- 注意：这里更新的是风格代码，不是显示名称，所以通常不需要修改
-- 但如果有存储显示名称的地方，需要相应更新

-- 3. 更新 reply_history 表中的风格记录（如果有存储风格名称的字段）
-- 检查是否有存储风格显示名称的字段需要更新
-- 这里假设存储的是风格代码，所以不需要更新

-- 4. 验证更新结果
SELECT 
    config_key,
    config_value,
    config_desc
FROM system_config 
WHERE config_key = 'supported_reply_styles';

-- 5. 显示更新完成信息
SELECT '回复风格名称更新完成！' as message;
SELECT '更新内容：' as info;
SELECT '- 逗比 → 玩梗' as change1;
SELECT '- 发表文学 → 文艺风格' as change2;
SELECT '- 话痨延申 → 话痨风格' as change3;

-- 6. 检查用户设置表中是否有需要更新的数据
SELECT 
    COUNT(*) as user_settings_count,
    '用户个性化设置记录数' as description
FROM user_settings;

-- 7. 检查历史记录表中的数据
SELECT 
    COUNT(*) as history_count,
    '历史记录数' as description
FROM reply_history;

-- =====================================================
-- 执行说明：
-- 1. 在MySQL客户端或管理工具中执行此脚本
-- 2. 确保数据库连接正常
-- 3. 建议在执行前备份数据库
-- 4. 执行后检查验证结果
-- =====================================================
