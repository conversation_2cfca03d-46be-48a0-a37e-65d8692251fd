<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后端连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1976D2;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.online {
            background: #d4edda;
            color: #155724;
        }
        .status.offline {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>🔧 后端连接测试</h1>
    
    <div class="test-section">
        <h2>后端服务状态检查</h2>
        <p>检查后端服务是否正常运行</p>
        
        <button onclick="checkBackendStatus()">检查后端状态</button>
        <button onclick="testHealthCheck()">健康检查</button>
        <button onclick="testCORS()">测试跨域</button>
        
        <div id="statusResult" class="status offline">后端状态：未检查</div>
        <div id="testResult" class="result">点击按钮开始测试...</div>
    </div>

    <div class="test-section">
        <h2>用户设置API测试</h2>
        <p>测试用户设置相关的API接口</p>
        
        <button onclick="testUserSettingsAPI()">测试用户设置API</button>
        
        <div id="userApiResult" class="result">等待测试...</div>
    </div>

    <div class="test-section">
        <h2>网络诊断</h2>
        <p>诊断网络连接问题</p>
        
        <button onclick="runNetworkDiagnostic()">运行网络诊断</button>
        
        <div id="diagnosticResult" class="result">等待诊断...</div>
    </div>

    <script>
        const BASE_URLS = [
            'http://localhost:8080',
            'http://127.0.0.1:8080',
            'http://localhost:8081',
            'http://127.0.0.1:8081'
        ];
        
        let workingBaseUrl = null;

        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    ...options,
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    }
                });
                
                return {
                    success: true,
                    status: response.status,
                    statusText: response.statusText,
                    data: await response.json().catch(() => response.text())
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message,
                    type: error.name
                };
            }
        }

        async function checkBackendStatus() {
            const statusDiv = document.getElementById('statusResult');
            const resultDiv = document.getElementById('testResult');
            
            statusDiv.textContent = '检查中...';
            statusDiv.className = 'status';
            
            let output = '后端服务状态检查:\n\n';
            
            for (const baseUrl of BASE_URLS) {
                output += `测试 ${baseUrl}:\n`;
                
                // 测试根路径
                const rootResult = await makeRequest(baseUrl);
                if (rootResult.success) {
                    output += `  ✅ 连接成功 (${rootResult.status})\n`;
                    workingBaseUrl = baseUrl;
                    statusDiv.textContent = `后端状态：在线 (${baseUrl})`;
                    statusDiv.className = 'status online';
                } else {
                    output += `  ❌ 连接失败: ${rootResult.error}\n`;
                }
                
                // 测试健康检查端点
                const healthResult = await makeRequest(`${baseUrl}/actuator/health`);
                if (healthResult.success) {
                    output += `  ✅ 健康检查通过\n`;
                } else {
                    output += `  ❌ 健康检查失败\n`;
                }
                
                output += '\n';
            }
            
            if (!workingBaseUrl) {
                statusDiv.textContent = '后端状态：离线';
                statusDiv.className = 'status offline';
                output += '❌ 所有后端地址都无法连接\n';
                output += '\n请检查:\n';
                output += '1. 后端服务是否启动\n';
                output += '2. 端口是否正确 (默认8080)\n';
                output += '3. 防火墙设置\n';
            }
            
            resultDiv.textContent = output;
            resultDiv.className = workingBaseUrl ? 'result success' : 'result error';
        }

        async function testHealthCheck() {
            if (!workingBaseUrl) {
                await checkBackendStatus();
                if (!workingBaseUrl) return;
            }
            
            const result = await makeRequest(`${workingBaseUrl}/actuator/health`);
            const resultDiv = document.getElementById('testResult');
            
            let output = '健康检查结果:\n\n';
            if (result.success) {
                output += `状态: ${result.status}\n`;
                output += `响应: ${JSON.stringify(result.data, null, 2)}`;
            } else {
                output += `错误: ${result.error}`;
            }
            
            resultDiv.textContent = output;
            resultDiv.className = result.success ? 'result success' : 'result error';
        }

        async function testCORS() {
            if (!workingBaseUrl) {
                await checkBackendStatus();
                if (!workingBaseUrl) return;
            }
            
            const result = await makeRequest(`${workingBaseUrl}/user/settings/1/reply-styles`, {
                method: 'GET',
                mode: 'cors'
            });
            
            const resultDiv = document.getElementById('testResult');
            
            let output = 'CORS测试结果:\n\n';
            if (result.success) {
                output += `✅ CORS配置正常\n`;
                output += `状态: ${result.status}\n`;
                output += `响应: ${JSON.stringify(result.data, null, 2)}`;
            } else {
                output += `❌ CORS问题: ${result.error}\n`;
                if (result.type === 'TypeError') {
                    output += '\n可能的原因:\n';
                    output += '1. 后端服务未启动\n';
                    output += '2. CORS配置问题\n';
                    output += '3. 网络连接问题\n';
                }
            }
            
            resultDiv.textContent = output;
            resultDiv.className = result.success ? 'result success' : 'result error';
        }

        async function testUserSettingsAPI() {
            if (!workingBaseUrl) {
                await checkBackendStatus();
                if (!workingBaseUrl) return;
            }
            
            const resultDiv = document.getElementById('userApiResult');
            let output = '用户设置API测试:\n\n';
            
            // 测试用户1, 2, 3
            for (let userId = 1; userId <= 3; userId++) {
                output += `测试用户 ${userId}:\n`;
                
                const result = await makeRequest(`${workingBaseUrl}/user/settings/${userId}/reply-styles`);
                
                if (result.success) {
                    output += `  ✅ API调用成功 (${result.status})\n`;
                    if (result.data && result.data.data) {
                        const data = result.data.data;
                        output += `  generationMode: ${data.generationMode}\n`;
                        output += `  primaryStyle: ${data.primaryStyle}\n`;
                        output += `  preferredCount: ${data.preferredCount}\n`;
                    }
                } else {
                    output += `  ❌ API调用失败: ${result.error}\n`;
                }
                output += '\n';
            }
            
            resultDiv.textContent = output;
            resultDiv.className = 'result';
        }

        async function runNetworkDiagnostic() {
            const resultDiv = document.getElementById('diagnosticResult');
            
            let output = '网络诊断报告:\n\n';
            
            // 检查浏览器信息
            output += `浏览器: ${navigator.userAgent}\n`;
            output += `协议: ${window.location.protocol}\n`;
            output += `主机: ${window.location.host}\n\n`;
            
            // 检查网络连接
            output += '网络连接状态:\n';
            if (navigator.onLine) {
                output += '  ✅ 浏览器显示在线\n';
            } else {
                output += '  ❌ 浏览器显示离线\n';
            }
            
            // 检查本地服务
            output += '\n本地服务检查:\n';
            for (const baseUrl of BASE_URLS) {
                const start = Date.now();
                const result = await makeRequest(baseUrl);
                const duration = Date.now() - start;
                
                if (result.success) {
                    output += `  ✅ ${baseUrl} - 响应时间: ${duration}ms\n`;
                } else {
                    output += `  ❌ ${baseUrl} - ${result.error}\n`;
                }
            }
            
            // 检查常见问题
            output += '\n常见问题检查:\n';
            if (window.location.protocol === 'file:') {
                output += '  ⚠️  从文件系统运行，可能有CORS限制\n';
            }
            
            if (window.location.protocol === 'https:' && BASE_URLS[0].startsWith('http:')) {
                output += '  ⚠️  HTTPS页面访问HTTP服务可能被阻止\n';
            }
            
            resultDiv.textContent = output;
            resultDiv.className = 'result';
        }

        // 页面加载时自动检查后端状态
        window.onload = function() {
            console.log('后端连接测试工具已加载');
            setTimeout(checkBackendStatus, 1000);
        };
    </script>
</body>
</html>
