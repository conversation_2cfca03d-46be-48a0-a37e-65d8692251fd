<template>
  <view class="container themed" :class="themeClass">
    <!-- 筛选器 -->
    <view class="filter-section">
      <view class="filter-tabs">
        <text 
          v-for="(tab, index) in filterTabs" 
          :key="index"
          :class="['tab-item', { active: currentTab === tab.value }]"
          @click="switchTab(tab.value)"
        >
          {{ tab.label }}
        </text>
      </view>
    </view>
    
    <!-- 历史记录列表 -->
    <view class="history-list">
      <view v-if="loading" class="loading">
        <text>加载中...</text>
      </view>
      
      <view v-else-if="historyList.length === 0" class="empty">
        <text>暂无历史记录</text>
      </view>
      
      <view v-else>
        <view 
          v-for="(item, index) in historyList" 
          :key="index"
          class="history-item"
          @click="viewDetail(item)"
        >
          <view class="item-header">
            <text class="time">{{ formatTime(item.createTime) }}</text>
            <view class="actions">
              <text 
                :class="['favorite-btn', { active: item.isFavorite }]"
                @click.stop="toggleFavorite(item)"
              >
                {{ item.isFavorite ? '❤️' : '🤍' }}
              </text>
              <text class="delete-btn" @click.stop="deleteItem(item)">🗑️</text>
            </view>
          </view>
          
          <view class="item-content">
            <text class="original-message">{{ item.originalMessage }}</text>
            <text class="reply-preview">{{ item.selectedReply }}</text>
          </view>
          
          <view class="item-footer">
            <text class="style-tag">{{ getStyleName(item.replyStyle) }}</text>
            <text class="emotion-tag">{{ item.emotion }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 加载更多 -->
    <view v-if="hasMore && !loading" class="load-more" @click="loadMore">
      <text>加载更多</text>
    </view>
  </view>
</template>

<script>
import { HistoryManager } from '../../utils/storage.js'
import { getHistoryList, getFavoriteHistory, toggleFavorite, deleteHistory } from '../../api/history.js'
import { UserManager } from '../../utils/user.js'
import { AuthGuard } from '../../utils/auth-guard.js'
import themeManager from '../../utils/theme.js'

export default {
  name: 'HistoryPage',
  
  data() {
    return {
      currentTab: 'all',
      loading: false,
      hasMore: true,
      page: 1,
      filterTabs: [
        { label: '全部', value: 'all' },
        { label: '收藏', value: 'favorite' },
        { label: '最近', value: 'recent' }
      ],
      historyList: [],
      themeClass: 'theme-auto'
    }
  },
  
  computed: {
    // 是否已登录
    isLoggedIn() {
      return UserManager.isLoggedIn()
    }
  },

  onLoad() {
    // 路由守卫检查
    if (!AuthGuard.pageGuard('pages/history/history')) {
      return
    }

    this.loadHistoryData()
    this.initTheme()
  },
  
  onPullDownRefresh() {
    this.refreshData()
  },
  
  onReachBottom() {
    if (this.hasMore && !this.loading) {
      this.loadMore()
    }
  },
  
  methods: {
    // 加载历史数据
    async loadHistoryData() {
      // 再次检查登录状态
      if (!this.isLoggedIn) {
        this.historyList = []
        return
      }

      this.loading = true

      try {
        let apiData = []

        // 尝试从API获取数据
        try {
          const currentUserId = UserManager.getCurrentUserId()
          if (this.currentTab === 'favorite') {
            apiData = await getFavoriteHistory(this.page, 20, currentUserId)
          } else {
            apiData = await getHistoryList(this.page, 20, currentUserId)
          }

          // 转换API数据格式
          const formattedData = apiData.map(item => {
            // 解析回复列表
            let replyList = []
            let selectedReply = '暂无回复'
            let replyStyle = 'unknown'

            try {
              if (item.replyList) {
                // 如果是字符串，解析JSON
                replyList = typeof item.replyList === 'string'
                  ? JSON.parse(item.replyList)
                  : item.replyList

                // 查找选中的回复
                if (item.selectedReply && replyList.length > 0) {
                  const selectedItem = replyList.find(reply => reply.content === item.selectedReply)
                  if (selectedItem) {
                    selectedReply = selectedItem.content
                    replyStyle = selectedItem.style
                  } else {
                    // 如果没找到匹配的，使用第一个
                    selectedReply = replyList[0].content
                    replyStyle = replyList[0].style
                  }
                } else if (replyList.length > 0) {
                  // 如果没有selectedReply但有回复列表，使用第一个
                  selectedReply = replyList[0].content
                  replyStyle = replyList[0].style
                }
              }
            } catch (parseError) {
              console.error('解析回复列表失败:', parseError)
            }

            return {
              id: item.id,
              originalMessage: item.originalMessage,
              selectedReply: selectedReply,
              replyStyle: replyStyle,
              emotion: item.emotionResult || '未知',
              isFavorite: item.isFavorite === 1,
              createTime: new Date(item.createTime)
            }
          })

          this.historyList = this.page === 1 ? formattedData : [...this.historyList, ...formattedData]
          this.hasMore = formattedData.length >= 20


        } catch (apiError) {

          // API调用失败时使用本地存储数据
          const allHistory = HistoryManager.getHistory()
          const formattedData = allHistory.map(item => ({
            id: item.id,
            originalMessage: item.originalMessage,
            selectedReply: item.replyList && item.replyList.length > 0
              ? item.replyList[0].content
              : '暂无回复',
            replyStyle: item.replyList && item.replyList.length > 0
              ? item.replyList[0].style
              : 'unknown',
            emotion: item.emotionResult ? item.emotionResult.emotion : '未知',
            isFavorite: item.isFavorite,
            createTime: new Date(item.createTime)
          }))

          this.historyList = this.filterData(formattedData)
          this.hasMore = false
        }

      } catch (error) {
        console.error('Load history error:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    
    // 切换标签
    switchTab(tab) {
      this.currentTab = tab
      this.page = 1
      this.loadHistoryData()
    },
    
    // 筛选数据
    filterData(data) {
      switch (this.currentTab) {
        case 'favorite':
          return data.filter(item => item.isFavorite)
        case 'recent':
          return data.filter(item => {
            const now = new Date()
            const itemTime = new Date(item.createTime)
            return (now - itemTime) < 24 * 60 * 60 * 1000 // 24小时内
          })
        default:
          return data
      }
    },
    
    // 查看详情
    viewDetail(item) {
      // 跳转到历史详情页面，而不是重新生成
      uni.navigateTo({
        url: `/pages/history/detail?id=${item.id}`
      })
    },
    
    // 切换收藏状态
    async toggleFavorite(item) {
      try {
        // 获取当前用户ID
        const currentUserId = UserManager.getCurrentUserId()
        // 尝试调用API
        await toggleFavorite(item.id, currentUserId)
        item.isFavorite = !item.isFavorite
        uni.showToast({
          title: item.isFavorite ? '已收藏' : '已取消收藏',
          icon: 'success'
        })
      } catch (error) {
        // API调用失败时使用本地操作
        const newStatus = HistoryManager.toggleFavorite(item.id)
        item.isFavorite = newStatus
        uni.showToast({
          title: newStatus ? '已收藏' : '已取消收藏',
          icon: 'success'
        })
      }
    },
    
    // 删除记录
    deleteItem(item) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这条记录吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              // 获取当前用户ID
              const currentUserId = UserManager.getCurrentUserId()
              // 尝试调用API删除
              await deleteHistory(item.id, currentUserId)

              const index = this.historyList.findIndex(h => h.id === item.id)
              if (index > -1) {
                this.historyList.splice(index, 1)
              }
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              })
            } catch (error) {
              console.error('删除API调用失败:', error)
              // API调用失败时使用本地删除
              const success = HistoryManager.removeHistory(item.id)
              if (success) {
                const index = this.historyList.findIndex(h => h.id === item.id)
                if (index > -1) {
                  this.historyList.splice(index, 1)
                }
                uni.showToast({
                  title: '删除成功',
                  icon: 'success'
                })
              } else {
                uni.showToast({
                  title: '删除失败',
                  icon: 'none'
                })
              }
            }
          }
        }
      })
    },
    
    // 刷新数据
    refreshData() {
      this.page = 1
      this.loadHistoryData().finally(() => {
        uni.stopPullDownRefresh()
      })
    },
    
    // 加载更多
    loadMore() {
      this.page++
      // 这里应该调用API加载更多数据
      // 暂时模拟没有更多数据
      this.hasMore = false
    },
    
    // 格式化时间
    formatTime(time) {
      const now = new Date()
      const target = new Date(time)
      const diff = now - target
      
      if (diff < 60000) { // 1分钟内
        return '刚刚'
      } else if (diff < 3600000) { // 1小时内
        return Math.floor(diff / 60000) + '分钟前'
      } else if (diff < 86400000) { // 1天内
        return Math.floor(diff / 3600000) + '小时前'
      } else {
        return target.toLocaleDateString()
      }
    },
    
    // 获取风格名称
    getStyleName(style) {
      const styleMap = {
        warm_caring: '温暖关怀',
        humorous: '幽默风趣',
        rational: '理性分析',
        concise: '简洁直接',
        romantic: '浪漫情话',
        unknown: '未知风格'
      }
      return styleMap[style] || '其他风格'
    },
    
    // 延迟函数
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    },

    // 初始化主题
    initTheme() {
      try {
        this.themeClass = `theme-${themeManager.getCurrentTheme()}`
      } catch (error) {
        console.error('初始化主题失败:', error)
        this.themeClass = 'theme-auto'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../styles/theme.scss';

.container {
  background-color: var(--bg-color);
  color: var(--text-color);
  min-height: 100vh;
  transition: all 0.3s ease;
}

.filter-section {
  background: var(--card-bg);
  border-bottom: 1px solid var(--border-color);
  padding: 20rpx;
  transition: all 0.3s ease;
  
  .filter-tabs {
    display: flex;
    
    .tab-item {
      flex: 1;
      text-align: center;
      padding: 16rpx 0;
      font-size: 28rpx;
      color: var(--text-color-secondary);
      border-bottom: 4rpx solid transparent;
      transition: all 0.3s ease;

      &.active {
        color: var(--primary-color);
        border-bottom-color: var(--primary-color);
        font-weight: bold;
      }
    }
  }
}

.history-list {
  padding: 20rpx;
  
  .loading, .empty {
    text-align: center;
    padding: 100rpx 0;
    
    text {
      font-size: 28rpx;
      color: var(--text-color-secondary);
      transition: color 0.3s ease;
    }
  }
  
  .history-item {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    transition: all 0.3s ease;
    
    .item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;
      
      .time {
        font-size: 24rpx;
        color: var(--text-color-muted);
        transition: color 0.3s ease;
      }
      
      .actions {
        display: flex;
        align-items: center;
        
        .favorite-btn, .delete-btn {
          margin-left: 20rpx;
          font-size: 32rpx;
          
          &.active {
            color: #ff4757;
          }
        }
      }
    }
    
    .item-content {
      margin-bottom: 20rpx;
      
      .original-message {
        display: block;
        font-size: 28rpx;
        color: var(--text-color);
        margin-bottom: 12rpx;
        font-weight: bold;
        transition: color 0.3s ease;
      }

      .reply-preview {
        font-size: 26rpx;
        color: var(--text-color-secondary);
        line-height: 1.5;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        transition: color 0.3s ease;
      }
    }
    
    .item-footer {
      display: flex;
      align-items: center;
      
      .style-tag, .emotion-tag {
        font-size: 22rpx;
        padding: 6rpx 12rpx;
        border-radius: 12rpx;
        margin-right: 16rpx;
      }
      
      .style-tag {
        background: rgba(33, 150, 243, 0.1);
        color: #2196F3;
      }
      
      .emotion-tag {
        background: rgba(255, 193, 7, 0.1);
        color: #FFC107;
      }
    }
  }
}

.load-more {
  text-align: center;
  padding: 40rpx 0;
  
  text {
    font-size: 28rpx;
    color: #2196F3;
  }
}
</style>
