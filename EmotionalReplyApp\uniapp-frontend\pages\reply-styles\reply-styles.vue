<template>
  <view class="page" :class="themeClass">
    <view class="header">
      <text class="title">🎭 回复风格设置</text>
      <text class="subtitle">当前模式：{{ getCurrentModeText() }}</text>
    </view>

    <!-- 生成模式选择 -->
    <view class="mode-section">
      <text class="section-title">生成模式</text>
      <text class="current-mode">当前：{{ getCurrentModeText() }}</text>
      <view class="mode-options">
        <view 
          class="mode-item" 
          :class="{ active: replySettings.generationMode === 'smart' }"
          @click="selectMode('smart')"
        >
          <text class="mode-icon">🧠</text>
          <view class="mode-content">
            <text class="mode-name">智能选择</text>
            <text class="mode-desc">根据情感自动匹配最合适的风格</text>
          </view>
        </view>
        
        <view 
          class="mode-item" 
          :class="{ active: replySettings.generationMode === 'custom' }"
          @click="selectMode('custom')"
        >
          <text class="mode-icon">🎯</text>
          <view class="mode-content">
            <text class="mode-name">自定义风格</text>
            <text class="mode-desc">使用您选择的风格组合</text>
          </view>
        </view>
        
        <view 
          class="mode-item" 
          :class="{ active: replySettings.generationMode === 'single' }"
          @click="selectMode('single')"
        >
          <text class="mode-icon">⚡</text>
          <view class="mode-content">
            <text class="mode-name">单一风格</text>
            <text class="mode-desc">只使用一种主要风格</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 回复数量设置 -->
    <view class="count-section" v-if="replySettings.generationMode !== 'single'">
      <text class="section-title">生成数量</text>
      <view class="count-options">
        <view 
          class="count-item" 
          :class="{ active: replySettings.preferredCount === 1 }"
          @click="selectCount(1)"
        >
          <text>1个</text>
        </view>
        <view 
          class="count-item" 
          :class="{ active: replySettings.preferredCount === 2 }"
          @click="selectCount(2)"
        >
          <text>2个</text>
        </view>
        <view 
          class="count-item" 
          :class="{ active: replySettings.preferredCount === 3 }"
          @click="selectCount(3)"
        >
          <text>3个</text>
        </view>
      </view>
    </view>

    <!-- 风格选择 -->
    <view class="styles-section" v-if="replySettings.generationMode === 'custom'">
      <text class="section-title">选择风格（最多{{ replySettings.preferredCount }}个）</text>
      <view class="styles-grid">
        <view 
          v-for="(styleName, styleKey) in replyStyles" 
          :key="styleKey"
          class="style-item" 
          :class="{ 
            active: replySettings.selectedStyles.includes(styleKey),
            disabled: !replySettings.selectedStyles.includes(styleKey) && replySettings.selectedStyles.length >= replySettings.preferredCount
          }"
          @click="toggleStyle(styleKey)"
        >
          <text class="style-emoji">{{ getStyleEmoji(styleKey) }}</text>
          <text class="style-name">{{ styleName }}</text>
        </view>
      </view>
    </view>

    <!-- 主要风格选择 -->
    <view class="primary-section" v-if="replySettings.generationMode === 'single'">
      <text class="section-title">选择主要风格</text>
      <view class="styles-grid">
        <view 
          v-for="(styleName, styleKey) in replyStyles" 
          :key="styleKey"
          class="style-item" 
          :class="{ active: replySettings.primaryStyle === styleKey }"
          @click="selectPrimaryStyle(styleKey)"
        >
          <text class="style-emoji">{{ getStyleEmoji(styleKey) }}</text>
          <text class="style-name">{{ styleName }}</text>
        </view>
      </view>
    </view>

    <!-- 保存按钮 -->
    <view class="save-section">
      <button class="save-btn" @click="saveSettings" :disabled="saving">
        <text v-if="saving">保存中...</text>
        <text v-else>保存设置</text>
      </button>
    </view>
  </view>
</template>

<script>
import { getReplyStyles } from '../../api/emotion.js'
import { getUserReplyPreferences, updateReplyPreferences } from '../../api/user.js'
import { UserManager } from '../../utils/user.js'
import themeManager from '../../utils/theme.js'

export default {
  name: 'ReplyStyles',
  
  data() {
    return {
      replySettings: {
        generationMode: 'smart',
        preferredCount: 2,
        primaryStyle: 'warm_caring',
        selectedStyles: ['warm_caring', 'humorous'] // 与 preferredCount 保持一致
      },
      replyStyles: {},
      saving: false,
      themeClass: 'theme-auto'
    }
  },
  
  async onLoad() {
    this.initTheme()
    await this.loadReplyStyles()
    await this.loadUserSettings()
  },
  
  methods: {
    // 初始化主题
    initTheme() {
      try {
        this.themeClass = `theme-${themeManager.getCurrentTheme()}`
      } catch (error) {
        console.error('初始化主题失败:', error)
        this.themeClass = 'theme-auto'
      }
    },
    
    // 加载回复风格
    async loadReplyStyles() {
      try {
        this.replyStyles = await getReplyStyles()
      } catch (error) {
        console.error('加载回复风格失败:', error)
        // 使用默认风格
        this.replyStyles = {
          warm_caring: '暖男',
          humorous: '玩梗',
          romantic: '撩妹',
          high_eq: '高情商',
          direct: '直接',
          mature: '成熟稳重',
          gentle: '温柔大叔',
          dominant: '霸道总裁',
          literary: '文艺风格',
          detailed: '话痨风格'
        }
      }
    },
    
    // 加载用户设置
    async loadUserSettings() {
      try {
        const userId = UserManager.getCurrentUserId()
        if (!userId) return
        
        const response = await getUserReplyPreferences(userId)
        if (response && response.data) {
          const data = response.data

          // 处理 replyStyles 字段 - 可能是数组或字符串
          let selectedStyles = ['warm_caring', 'humorous', 'high_eq'] // 默认值
          if (data.replyStyles) {
            if (Array.isArray(data.replyStyles)) {
              selectedStyles = data.replyStyles
            } else if (typeof data.replyStyles === 'string') {
              try {
                selectedStyles = JSON.parse(data.replyStyles)
              } catch (e) {
                console.warn('解析 replyStyles 失败:', e)
              }
            }
          }

          this.replySettings = {
            generationMode: data.generationMode || 'smart',
            preferredCount: data.preferredCount || 2,
            primaryStyle: data.primaryStyle || 'warm_caring',
            selectedStyles: selectedStyles
          }
        }
      } catch (error) {
        console.log('加载用户设置失败，使用默认值:', error)
      }
    },
    
    // 选择生成模式
    selectMode(mode) {
      this.replySettings.generationMode = mode

      // 如果切换到单一模式，确保有主要风格
      if (mode === 'single' && !this.replySettings.primaryStyle) {
        this.replySettings.primaryStyle = 'warm_caring'
      }

      // 如果切换到自定义模式，确保有选中的风格，但不改变数量
      if (mode === 'custom' && this.replySettings.selectedStyles.length === 0) {
        // 根据当前设置的数量来选择默认风格
        const defaultStyles = ['warm_caring', 'humorous', 'high_eq']
        this.replySettings.selectedStyles = defaultStyles.slice(0, this.replySettings.preferredCount)
      }

      // 如果当前选中的风格数量超过了设置的数量，截取到合适的数量
      if (mode === 'custom' && this.replySettings.selectedStyles.length > this.replySettings.preferredCount) {
        this.replySettings.selectedStyles = this.replySettings.selectedStyles.slice(0, this.replySettings.preferredCount)
      }
    },
    
    // 选择生成数量
    selectCount(count) {
      this.replySettings.preferredCount = count
      
      // 如果当前选中的风格超过了数量限制，截取前几个
      if (this.replySettings.selectedStyles.length > count) {
        this.replySettings.selectedStyles = this.replySettings.selectedStyles.slice(0, count)
      }
    },
    
    // 切换风格选择
    toggleStyle(styleKey) {
      const index = this.replySettings.selectedStyles.indexOf(styleKey)
      
      if (index > -1) {
        // 已选中，取消选择
        this.replySettings.selectedStyles.splice(index, 1)
      } else {
        // 未选中，添加选择
        if (this.replySettings.selectedStyles.length < this.replySettings.preferredCount) {
          this.replySettings.selectedStyles.push(styleKey)
        } else {
          uni.showToast({
            title: `最多选择${this.replySettings.preferredCount}个风格`,
            icon: 'none'
          })
        }
      }
    },
    
    // 选择主要风格
    selectPrimaryStyle(styleKey) {
      this.replySettings.primaryStyle = styleKey
    },
    
    // 获取风格对应的emoji
    getStyleEmoji(styleKey) {
      const emojiMap = {
        warm_caring: '🤗',
        humorous: '😄',
        romantic: '💋',
        high_eq: '🧠',
        direct: '🎯',
        mature: '🎩',
        gentle: '🧔',
        dominant: '👑',
        literary: '📚',
        detailed: '💬'
      }
      return emojiMap[styleKey] || '🎭'
    },

    // 获取当前模式的中文名称
    getCurrentModeText() {
      const modeMap = {
        'smart': '智能选择',
        'custom': '自定义风格',
        'single': '单一风格'
      }
      return modeMap[this.replySettings.generationMode] || '智能选择'
    },

    // 保存设置
    async saveSettings() {
      this.saving = true
      
      try {
        const userId = UserManager.getCurrentUserId()
        if (!userId) {
          uni.showToast({ title: '请先登录', icon: 'none' })
          return
        }
        
        await updateReplyPreferences(userId, {
          generationMode: this.replySettings.generationMode,
          preferredCount: this.replySettings.preferredCount,
          primaryStyle: this.replySettings.primaryStyle,
          replyStyles: JSON.stringify(this.replySettings.selectedStyles)
        })
        
        // 保存到本地
        uni.setStorageSync('replySettings', this.replySettings)
        
        uni.showToast({ title: '设置已保存', icon: 'success' })
        
        // 延迟返回上一页
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
        
      } catch (error) {
        console.error('保存设置失败:', error)
        uni.showToast({ title: '保存失败', icon: 'none' })
      } finally {
        this.saving = false
      }
    }
  }
}
</script>

<style scoped>
@import '../../styles/theme.scss';

.page {
  padding: 40rpx;
  background: var(--bg-color);
  color: var(--text-color);
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 20rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: var(--text-color-secondary);
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: 10rpx;
}

.current-mode {
  display: block;
  font-size: 24rpx;
  color: var(--primary-color);
  background: rgba(33, 150, 243, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  text-align: center;
  font-weight: 500;
}

/* 模式选择样式 */
.mode-section {
  margin-bottom: 60rpx;
}

.mode-options {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.mode-item {
  display: flex;
  align-items: center;
  background: var(--card-bg);
  border: 2rpx solid var(--border-color);
  border-radius: 20rpx;
  padding: 30rpx;
  transition: all 0.3s ease;
}

.mode-item.active {
  border-color: var(--primary-color);
  background: rgba(33, 150, 243, 0.1);
}

.mode-icon {
  font-size: 48rpx;
  margin-right: 30rpx;
}

.mode-content {
  flex: 1;
}

.mode-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: 10rpx;
}

.mode-desc {
  display: block;
  font-size: 24rpx;
  color: var(--text-color-secondary);
}

/* 数量选择样式 */
.count-section {
  margin-bottom: 60rpx;
}

.count-options {
  display: flex;
  gap: 20rpx;
}

.count-item {
  flex: 1;
  background: var(--card-bg);
  border: 2rpx solid var(--border-color);
  border-radius: 16rpx;
  padding: 30rpx;
  text-align: center;
  font-size: 28rpx;
  color: var(--text-color);
  transition: all 0.3s ease;
}

.count-item.active {
  border-color: var(--primary-color);
  background: rgba(33, 150, 243, 0.1);
  color: var(--primary-color);
}

/* 风格选择样式 */
.styles-section, .primary-section {
  margin-bottom: 60rpx;
}

.styles-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.style-item {
  background: var(--card-bg);
  border: 2rpx solid var(--border-color);
  border-radius: 20rpx;
  padding: 30rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.style-item.active {
  border-color: var(--primary-color);
  background: rgba(33, 150, 243, 0.1);
}

.style-item.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.style-emoji {
  display: block;
  font-size: 48rpx;
  margin-bottom: 15rpx;
}

.style-name {
  display: block;
  font-size: 28rpx;
  color: var(--text-color);
  font-weight: bold;
}

/* 保存按钮样式 */
.save-section {
  margin-top: 80rpx;
}

.save-btn {
  width: 100%;
  background: var(--primary-gradient);
  border: none;
  border-radius: 20rpx;
  padding: 40rpx;
  font-size: 32rpx;
  color: white;
  font-weight: bold;
}

.save-btn:disabled {
  opacity: 0.6;
}
</style>
