package com.emotional.service.controller;

import com.emotional.service.common.Result;
import com.emotional.service.entity.UserSettings;
import com.emotional.service.service.UserSettingsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户设置控制器
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/user/settings")
@RequiredArgsConstructor
@Validated
@CrossOrigin(originPatterns = "*", maxAge = 3600)
@Tag(name = "用户设置", description = "用户个性化设置相关接口")
public class UserSettingsController {

    private final UserSettingsService userSettingsService;
    
    /**
     * 获取用户设置
     */
    @Operation(summary = "获取用户设置", description = "获取指定用户的个性化设置")
    @GetMapping("/{userId}")
    public Result<UserSettings> getUserSettings(@PathVariable Long userId) {
        log.info("获取用户设置: userId={}", userId);
        
        try {
            UserSettings settings = userSettingsService.getByUserId(userId);
            return Result.success("获取成功", settings);
        } catch (Exception e) {
            log.error("获取用户设置失败: userId={}", userId, e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新用户设置
     */
    @Operation(summary = "更新用户设置", description = "更新用户的个性化设置")
    @PutMapping("/{userId}")
    public Result<String> updateUserSettings(
            @PathVariable Long userId,
            @Valid @RequestBody UserSettings settings) {
        
        log.info("更新用户设置: userId={}", userId);
        
        try {
            boolean success = userSettingsService.updateUserSettings(userId, settings);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新用户设置失败: userId={}", userId, e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户回复风格偏好
     */
    @Operation(summary = "获取回复风格偏好", description = "获取用户偏好的回复风格列表")
    @GetMapping("/{userId}/reply-styles")
    public Result<Map<String, Object>> getUserReplyPreferences(@PathVariable Long userId) {
        log.info("获取用户回复风格偏好: userId={}", userId);
        
        try {
            Map<String, Object> preferences = new HashMap<>();
            preferences.put("replyStyles", userSettingsService.getUserReplyStyles(userId));
            preferences.put("preferredCount", userSettingsService.getUserPreferredReplyCount(userId));
            preferences.put("generationMode", userSettingsService.getUserReplyGenerationMode(userId));
            preferences.put("primaryStyle", userSettingsService.getUserPrimaryStyle(userId));
            
            return Result.success("获取成功", preferences);
        } catch (Exception e) {
            log.error("获取用户回复风格偏好失败: userId={}", userId, e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新用户回复风格偏好
     */
    @Operation(summary = "更新回复风格偏好", description = "更新用户的回复风格偏好设置")
    @PutMapping("/{userId}/reply-styles")
    public Result<String> updateReplyPreferences(
            @PathVariable Long userId,
            @RequestBody Map<String, Object> preferences) {
        
        log.info("更新用户回复风格偏好: userId={}, preferences={}", userId, preferences);
        
        try {
            UserSettings settings = userSettingsService.getByUserId(userId);
            if (settings == null) {
                settings = userSettingsService.initDefaultSettings(userId);
            }
            
            // 更新回复相关设置
            if (preferences.containsKey("replyStyles")) {
                settings.setReplyStyles(preferences.get("replyStyles").toString());
            }
            if (preferences.containsKey("preferredCount")) {
                settings.setPreferredReplyCount((Integer) preferences.get("preferredCount"));
            }
            if (preferences.containsKey("generationMode")) {
                settings.setReplyGenerationMode((String) preferences.get("generationMode"));
            }
            if (preferences.containsKey("primaryStyle")) {
                settings.setPrimaryStyle((String) preferences.get("primaryStyle"));
            }
            
            boolean success = userSettingsService.updateUserSettings(userId, settings);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新用户回复风格偏好失败: userId={}", userId, e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }
    
    /**
     * 重置用户设置为默认值
     */
    @Operation(summary = "重置设置", description = "重置用户设置为默认值")
    @PostMapping("/{userId}/reset")
    public Result<String> resetUserSettings(@PathVariable Long userId) {
        log.info("重置用户设置: userId={}", userId);
        
        try {
            UserSettings defaultSettings = userSettingsService.initDefaultSettings(userId);
            if (defaultSettings != null) {
                return Result.success("重置成功");
            } else {
                return Result.error("重置失败");
            }
        } catch (Exception e) {
            log.error("重置用户设置失败: userId={}", userId, e);
            return Result.error("重置失败: " + e.getMessage());
        }
    }
}
