<template>
  <view class="container">
    <!-- 头部 -->
    <view class="header">
      <text class="title">历史详情</text>
    </view>
    
    <!-- 原始消息 -->
    <view class="original-message">
      <text class="label">收到的消息：</text>
      <text class="message-text">{{ historyData.originalMessage }}</text>
    </view>
    
    <!-- 情感分析结果 -->
    <view class="emotion-analysis" v-if="historyData.emotionResult">
      <text class="section-title">情感分析</text>
      <view class="emotion-tags">
        <text class="emotion-tag">{{ historyData.emotionResult }}</text>
        <text class="confidence" v-if="historyData.emotionConfidence">
          置信度: {{ Math.round(historyData.emotionConfidence * 100) }}%
        </text>
      </view>
    </view>
    
    <!-- 回复建议 -->
    <view class="reply-suggestions">
      <text class="section-title">回复建议</text>
      
      <view v-if="loading" class="loading">
        <text>加载中...</text>
      </view>
      
      <view v-else-if="replyList.length === 0" class="empty">
        <text>暂无回复建议</text>
      </view>
      
      <view v-else class="reply-list">
        <view 
          v-for="(reply, index) in replyList" 
          :key="index"
          class="reply-item"
          :class="{ selected: reply.content === historyData.selectedReply }"
        >
          <view class="reply-header">
            <text class="style-tag">{{ getStyleName(reply.style) }}</text>
            <view class="actions">
              <text class="copy-btn" @click="copyReply(reply.content)">复制</text>
              <text v-if="reply.content === historyData.selectedReply" class="selected-tag">已选择</text>
            </view>
          </view>
          <text class="reply-content">{{ reply.content }}</text>
        </view>
      </view>
    </view>
    
    <!-- 底部操作 -->
    <view class="bottom-actions">
      <button class="back-btn" @click="goBack">
        <text>返回</text>
      </button>
      <button class="regenerate-btn" @click="regenerateReplies">
        <text>重新生成</text>
      </button>
    </view>
  </view>
</template>

<script>
import { getHistoryDetail } from '../../api/history.js'
import { regenerateReply } from '../../api/emotion.js'
import { UserManager } from '../../utils/user.js'

export default {
  name: 'HistoryDetail',

  data() {
    return {
      historyId: null,
      loading: false,
      historyData: {
        originalMessage: '',
        emotionResult: '',
        emotionConfidence: 0,
        selectedReply: '',
        selectedStyle: ''
      },
      replyList: []
    }
  },

  onLoad(options) {
    if (options.id) {
      this.historyId = options.id
      this.loadHistoryDetail()
    } else {
      uni.showToast({
        title: '参数错误',
        icon: 'none'
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  },

  methods: {
    // 加载历史详情
    async loadHistoryDetail() {
      this.loading = true

      try {
        // 调用API获取历史详情
        const response = await getHistoryDetail(this.historyId)
        
        this.historyData = {
          originalMessage: response.originalMessage,
          emotionResult: response.emotionResult,
          emotionConfidence: response.emotionConfidence,
          selectedReply: response.selectedReply,
          selectedStyle: response.selectedStyle
        }

        // 解析回复列表
        if (response.replyList) {
          try {
            // 如果是字符串，需要解析JSON
            const replyListData = typeof response.replyList === 'string' 
              ? JSON.parse(response.replyList) 
              : response.replyList

            this.replyList = replyListData.map(option => ({
              content: option.content,
              style: option.style,
              styleName: option.styleName || this.getStyleName(option.style)
            }))
          } catch (parseError) {
            console.error('解析回复列表失败:', parseError)
            this.replyList = []
          }
        }

      } catch (error) {
        console.error('加载历史详情失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    // 复制回复
    copyReply(content) {
      uni.setClipboardData({
        data: content,
        success: () => {
          uni.showToast({
            title: '已复制到剪贴板',
            icon: 'success'
          })
        }
      })
    },

    // 重新生成回复
    async regenerateReplies() {
      uni.showModal({
        title: '重新生成',
        content: '确定要重新生成回复建议吗？这将调用AI服务重新分析。',
        success: async (res) => {
          if (res.confirm) {
            // 跳转到生成页面
            uni.navigateTo({
              url: `/pages/reply/generation?message=${encodeURIComponent(this.historyData.originalMessage)}`
            })
          }
        }
      })
    },

    // 返回
    goBack() {
      uni.navigateBack()
    },

    // 获取风格名称
    getStyleName(style) {
      const styleMap = {
        warm_caring: '温暖关怀',
        humorous: '幽默风趣',
        rational: '理性分析',
        concise: '简洁直接',
        romantic: '浪漫情话'
      }
      return styleMap[style] || style
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.header {
  text-align: center;
  padding: 20rpx 0;
  
  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.original-message {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .label {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 16rpx;
  }
  
  .message-text {
    font-size: 32rpx;
    color: #333;
    line-height: 1.5;
  }
}

.emotion-analysis {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }
  
  .emotion-tags {
    display: flex;
    align-items: center;
    
    .emotion-tag {
      background: #2196F3;
      color: white;
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      font-size: 24rpx;
      margin-right: 20rpx;
    }
    
    .confidence {
      font-size: 24rpx;
      color: #666;
    }
  }
}

.reply-suggestions {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 100rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 30rpx;
  }
  
  .loading, .empty {
    text-align: center;
    padding: 60rpx 0;
    
    text {
      font-size: 28rpx;
      color: #666;
    }
  }
  
  .reply-list {
    .reply-item {
      border: 2rpx solid #f0f0f0;
      border-radius: 12rpx;
      padding: 24rpx;
      margin-bottom: 20rpx;
      
      &.selected {
        border-color: #2196F3;
        background: rgba(33, 150, 243, 0.05);
      }
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .reply-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16rpx;
        
        .style-tag {
          background: rgba(33, 150, 243, 0.1);
          color: #2196F3;
          padding: 6rpx 12rpx;
          border-radius: 12rpx;
          font-size: 22rpx;
        }
        
        .actions {
          display: flex;
          align-items: center;
          
          .copy-btn, .selected-tag {
            margin-left: 20rpx;
            font-size: 24rpx;
          }
          
          .copy-btn {
            color: #2196F3;
          }
          
          .selected-tag {
            color: #4CAF50;
            font-weight: bold;
          }
        }
      }
      
      .reply-content {
        font-size: 28rpx;
        color: #333;
        line-height: 1.6;
      }
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx;
  display: flex;
  gap: 20rpx;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
  
  .back-btn, .regenerate-btn {
    flex: 1;
    height: 80rpx;
    border-radius: 40rpx;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    
    text {
      font-size: 28rpx;
      font-weight: bold;
    }
  }
  
  .back-btn {
    background: #f0f0f0;
    
    text {
      color: #333;
    }
  }
  
  .regenerate-btn {
    background: linear-gradient(135deg, #2196F3, #21CBF3);
    
    text {
      color: white;
    }
  }
}
</style>
