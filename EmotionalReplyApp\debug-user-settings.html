<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户设置调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1976D2;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .highlight {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <h1>🔧 用户设置调试工具</h1>
    
    <div class="test-section">
        <h2>用户回复偏好设置测试</h2>
        <p>测试不同用户的回复偏好设置数据</p>
        
        <button onclick="testUserSettings(1)">测试用户1</button>
        <button onclick="testUserSettings(2)">测试用户2</button>
        <button onclick="testUserSettings(3)">测试用户3</button>
        <button onclick="testAllUsers()">测试所有用户</button>
        
        <div id="userSettingsResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>数据库直接查询</h2>
        <p>查看数据库中的原始数据</p>
        
        <button onclick="testDatabaseQuery()">查询数据库</button>
        
        <div id="databaseResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>前端模式文本转换测试</h2>
        <p>测试前端的模式文本转换逻辑</p>
        
        <button onclick="testModeTextConversion()">测试模式转换</button>
        
        <div id="modeTextResult" class="result"></div>
    </div>

    <script>
        const BASE_URL = 'http://localhost:8080';

        async function makeRequest(url, method = 'GET', data = null) {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (data) {
                    options.body = JSON.stringify(data);
                }

                const response = await fetch(BASE_URL + url, options);
                const result = await response.json();
                return { success: true, data: result, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function testUserSettings(userId) {
            const result = await makeRequest(`/user/settings/${userId}/reply-styles`);
            
            let output = `用户 ${userId} 的回复偏好设置:\n\n`;
            
            if (result.success) {
                output += `HTTP状态: ${result.status}\n`;
                output += `响应数据:\n${JSON.stringify(result.data, null, 2)}\n\n`;
                
                if (result.data.success && result.data.data) {
                    const data = result.data.data;
                    output += `解析后的字段:\n`;
                    output += `- generationMode: ${data.generationMode}\n`;
                    output += `- preferredCount: ${data.preferredCount}\n`;
                    output += `- primaryStyle: ${data.primaryStyle}\n`;
                    output += `- replyStyles: ${data.replyStyles}\n\n`;
                    
                    // 模拟前端的模式文本转换
                    const modeMap = {
                        'smart': '智能选择',
                        'custom': '自定义风格',
                        'single': '单一风格'
                    };
                    const modeText = modeMap[data.generationMode] || '智能选择';
                    output += `前端显示文本: ${modeText}`;
                }
            } else {
                output += `错误: ${result.error}`;
            }
            
            document.getElementById('userSettingsResult').textContent = output;
            document.getElementById('userSettingsResult').className = result.success ? 'result success' : 'result error';
        }

        async function testAllUsers() {
            let output = '所有用户的回复偏好设置对比:\n\n';
            
            for (let userId = 1; userId <= 3; userId++) {
                const result = await makeRequest(`/user/settings/${userId}/reply-styles`);
                
                output += `用户 ${userId}:\n`;
                if (result.success && result.data.success && result.data.data) {
                    const data = result.data.data;
                    output += `  generationMode: ${data.generationMode}\n`;
                    output += `  preferredCount: ${data.preferredCount}\n`;
                    output += `  primaryStyle: ${data.primaryStyle}\n`;
                } else {
                    output += `  错误: 无法获取数据\n`;
                }
                output += '\n';
            }
            
            document.getElementById('userSettingsResult').textContent = output;
            document.getElementById('userSettingsResult').className = 'result highlight';
        }

        function testDatabaseQuery() {
            const output = `数据库查询SQL:\n\nSELECT user_id, reply_generation_mode, preferred_reply_count, primary_style, reply_styles \nFROM user_settings \nWHERE user_id IN (1, 2, 3);\n\n请在数据库中执行此查询来查看原始数据。\n\n根据之前的截图，数据库中应该有:\n- user_id=3: reply_generation_mode='custom'\n- 其他用户的数据...`;
            
            document.getElementById('databaseResult').textContent = output;
            document.getElementById('databaseResult').className = 'result highlight';
        }

        function testModeTextConversion() {
            const testCases = [
                { mode: 'smart', expected: '智能选择' },
                { mode: 'custom', expected: '自定义风格' },
                { mode: 'single', expected: '单一风格' },
                { mode: null, expected: '智能选择' },
                { mode: undefined, expected: '智能选择' },
                { mode: 'unknown', expected: '智能选择' }
            ];

            const modeMap = {
                'smart': '智能选择',
                'custom': '自定义风格',
                'single': '单一风格'
            };

            let output = '模式文本转换测试:\n\n';
            testCases.forEach((testCase, index) => {
                const result = modeMap[testCase.mode] || '智能选择';
                const passed = result === testCase.expected;
                output += `测试 ${index + 1}: ${passed ? '✅' : '❌'}\n`;
                output += `  输入: ${testCase.mode}\n`;
                output += `  期望: ${testCase.expected}\n`;
                output += `  实际: ${result}\n\n`;
            });

            document.getElementById('modeTextResult').textContent = output;
            document.getElementById('modeTextResult').className = 'result success';
        }

        // 页面加载时显示说明
        window.onload = function() {
            console.log('用户设置调试工具已加载');
            console.log('请确保后端服务运行在 http://localhost:8080');
        };
    </script>
</body>
</html>
