-- 更新普通用户配额从5次改为3次
-- 执行日期: 2025-01-26
-- 说明: 将普通用户每日配额从5次调整为3次

USE emotional_reply;

-- 1. 更新系统配置表中的默认配额
UPDATE system_config 
SET config_value = '3' 
WHERE config_key = 'default_daily_quota';

-- 2. 更新所有普通用户（非VIP、非管理员）的每日配额
UPDATE users 
SET daily_quota = 3 
WHERE is_vip = 0 AND is_admin = 0;

-- 3. 更新用户统计表中普通用户的配额
UPDATE user_stats 
SET daily_quota = 3 
WHERE is_vip = 0 
AND user_id IN (
    SELECT id FROM users WHERE is_vip = 0 AND is_admin = 0
);

-- 4. 验证更新结果
SELECT 
    '系统配置更新结果' as check_type,
    config_key,
    config_value,
    config_desc
FROM system_config 
WHERE config_key = 'default_daily_quota';

SELECT 
    '用户配额更新结果' as check_type,
    COUNT(*) as affected_users,
    daily_quota,
    CASE 
        WHEN is_admin = 1 THEN '管理员'
        WHEN is_vip = 1 THEN 'VIP用户'
        ELSE '普通用户'
    END as user_type
FROM users 
GROUP BY daily_quota, is_vip, is_admin
ORDER BY is_admin DESC, is_vip DESC;

SELECT 
    '用户统计表更新结果' as check_type,
    COUNT(*) as affected_records,
    daily_quota,
    CASE 
        WHEN is_vip = 1 THEN 'VIP用户'
        ELSE '普通用户'
    END as user_type
FROM user_stats 
GROUP BY daily_quota, is_vip
ORDER BY is_vip DESC;

-- 显示更新完成信息
SELECT 
    '更新完成' as status,
    '普通用户每日配额已从5次调整为3次' as message,
    NOW() as update_time;
