<template>
  <view class="container">
    <view class="header">
      <text class="title">激活码兑换</text>
      <text class="subtitle">输入激活码升级为VIP用户</text>
    </view>
    
    <view class="form-section">
      <view class="input-group">
        <text class="label">激活码</text>
        <input 
          class="input" 
          v-model="activationCode" 
          placeholder="请输入激活码，格式：XXXX-XXXX-XXXX-XXXX"
          maxlength="19"
          @input="formatCode"
        />
      </view>
      
      <button 
        class="submit-btn" 
        :disabled="!isValidFormat || loading"
        @click="useActivationCode"
      >
        <text v-if="loading">兑换中...</text>
        <text v-else>立即兑换</text>
      </button>
    </view>
    
    <view class="info-section">
      <view class="info-item">
        <text class="info-title">VIP权益</text>
        <text class="info-content">• 每日150次使用配额</text>
        <text class="info-content">• 全部回复风格</text>
        <text class="info-content">• 优先响应</text>
        <text class="info-content">• 历史记录保存</text>
      </view>
      
      <view class="info-item">
        <text class="info-title">使用说明</text>
        <text class="info-content">• 激活码只能使用一次</text>
        <text class="info-content">• VIP时间可以叠加</text>
        <text class="info-content">• 激活后立即生效</text>
      </view>
    </view>
  </view>
</template>

<script>
import { useActivationCode } from '../../api/activation.js'
import { UserManager } from '../../utils/user.js'

export default {
  name: 'ActivationPage',
  
  data() {
    return {
      activationCode: '',
      loading: false
    }
  },
  
  computed: {
    isValidFormat() {
      // 检查格式是否为 XXXX-XXXX-XXXX-XXXX
      const pattern = /^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/
      return pattern.test(this.activationCode)
    }
  },
  
  methods: {
    // 格式化激活码输入
    formatCode(e) {
      let value = e.detail.value.toUpperCase().replace(/[^A-Z0-9]/g, '')
      
      // 自动添加连字符
      if (value.length > 4) {
        value = value.substring(0, 4) + '-' + value.substring(4)
      }
      if (value.length > 9) {
        value = value.substring(0, 9) + '-' + value.substring(9)
      }
      if (value.length > 14) {
        value = value.substring(0, 14) + '-' + value.substring(14)
      }
      
      this.activationCode = value.substring(0, 19) // 限制最大长度
    },
    
    // 使用激活码
    async useActivationCode() {
      if (!this.isValidFormat) {
        uni.showToast({
          title: '请输入正确格式的激活码',
          icon: 'error'
        })
        return
      }
      
      const userInfo = UserManager.getUserInfo()
      if (!userInfo || !userInfo.id) {
        uni.showToast({
          title: '请先登录',
          icon: 'error'
        })
        return
      }
      
      this.loading = true
      
      try {
        const response = await useActivationCode(this.activationCode, userInfo.id)

        // 后端成功时返回的是data.data，这里response就是成功的数据
        // 如果没有抛出异常，说明激活码使用成功
        uni.showModal({
          title: '兑换成功',
          content: 'VIP权限已激活，请重新登录以刷新权限',
          showCancel: false,
          success: () => {
            // 清空激活码
            this.activationCode = ''
            // 返回上一页
            uni.navigateBack()
          }
        })

      } catch (error) {
        console.error('使用激活码失败:', error)
        // 错误信息已经在request.js的handleBusinessError中显示了
        // 这里不需要再次显示toast
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f8f9fa;
  min-height: 100vh;
  padding: 40rpx 30rpx;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
  
  .title {
    display: block;
    font-size: 48rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }
  
  .subtitle {
    font-size: 28rpx;
    color: #666;
  }
}

.form-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  
  .input-group {
    margin-bottom: 40rpx;
    
    .label {
      display: block;
      font-size: 28rpx;
      color: #333;
      margin-bottom: 20rpx;
      font-weight: bold;
    }
    
    .input {
      width: 100%;
      height: 88rpx;
      border: 2rpx solid #e0e0e0;
      border-radius: 12rpx;
      padding: 0 20rpx;
      font-size: 32rpx;
      font-family: 'Courier New', monospace;
      letter-spacing: 2rpx;
      
      &:focus {
        border-color: #2196F3;
      }
    }
  }
  
  .submit-btn {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #2196F3, #21CBF3);
    border-radius: 44rpx;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:disabled {
      background: #ccc;
    }
    
    text {
      color: white;
      font-size: 32rpx;
      font-weight: bold;
    }
  }
}

.info-section {
  .info-item {
    background: white;
    border-radius: 20rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    
    .info-title {
      display: block;
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 20rpx;
    }
    
    .info-content {
      display: block;
      font-size: 26rpx;
      color: #666;
      line-height: 1.6;
      margin-bottom: 10rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>
