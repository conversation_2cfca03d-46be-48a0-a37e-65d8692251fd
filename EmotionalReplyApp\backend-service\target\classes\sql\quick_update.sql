-- 快速更新回复风格名称
-- 执行命令: mysql -u root -p emotional_reply_db < quick_update.sql

USE emotional_reply_db;

-- 更新系统配置中的回复风格
UPDATE system_config 
SET config_value = '{"warm_caring": "暖男", "humorous": "玩梗", "romantic": "撩妹", "high_eq": "高情商", "direct": "直接", "mature": "成熟稳重", "gentle": "温柔大叔", "dominant": "霸道总裁", "literary": "文艺风格", "detailed": "话痨风格"}'
WHERE config_key = 'supported_reply_styles';

-- 验证更新结果
SELECT 
    '更新完成！' as status,
    config_value as new_styles
FROM system_config 
WHERE config_key = 'supported_reply_styles';
