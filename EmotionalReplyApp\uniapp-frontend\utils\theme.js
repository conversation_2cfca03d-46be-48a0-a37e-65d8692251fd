/**
 * 主题管理工具
 */

class ThemeManager {
  constructor() {
    this.currentTheme = 'auto'
    this.init()
  }

  /**
   * 初始化主题
   */
  init() {
    // 从本地存储获取主题设置
    const savedTheme = uni.getStorageSync('theme') || 'auto'
    this.setTheme(savedTheme)
  }

  /**
   * 设置主题
   * @param {string} theme - 主题名称：'light', 'dark', 'auto'
   */
  setTheme(theme) {
    this.currentTheme = theme

    // 保存到本地存储
    uni.setStorageSync('theme', theme)

    // 应用主题到页面
    this.applyTheme(theme)

    // 延迟设置状态栏样式，确保页面已加载
    setTimeout(() => {
      this.setStatusBarStyle(theme)
    }, 100)
  }

  /**
   * 应用主题到页面
   * @param {string} theme - 主题名称
   */
  applyTheme(theme) {
    try {
      // 获取页面根元素
      const pages = getCurrentPages()
      if (pages.length === 0) return
      
      const currentPage = pages[pages.length - 1]
      
      // 移除之前的主题类
      this.removeThemeClasses()
      
      // 添加新的主题类
      this.addThemeClass(`theme-${theme}`)
      
      // 触发页面重新渲染（如果需要）
      if (currentPage && currentPage.$vm) {
        currentPage.$vm.$forceUpdate && currentPage.$vm.$forceUpdate()
      }
      
    } catch (error) {
      console.error('应用主题失败:', error)
    }
  }

  /**
   * 移除所有主题类
   */
  removeThemeClasses() {
    try {
      // 在小程序环境中，我们通过设置全局样式变量来实现
      const app = getApp()
      if (app.globalData) {
        app.globalData.themeClass = ''
      }
    } catch (error) {
      console.error('移除主题类失败:', error)
    }
  }

  /**
   * 添加主题类
   * @param {string} className - 主题类名
   */
  addThemeClass(className) {
    try {
      const app = getApp()
      if (app.globalData) {
        app.globalData.themeClass = className
      }
    } catch (error) {
      console.error('添加主题类失败:', error)
    }
  }

  /**
   * 设置状态栏样式
   * @param {string} theme - 主题名称
   */
  setStatusBarStyle(theme) {
    // #ifdef APP-PLUS
    try {
      let style = 'dark' // 默认深色文字（适合浅色背景）

      if (theme === 'dark') {
        style = 'light' // 浅色文字（适合深色背景）
      } else if (theme === 'auto') {
        // 跟随系统
        const systemInfo = uni.getSystemInfoSync()
        if (systemInfo.theme === 'dark') {
          style = 'light'
        }
      }

      // 设置状态栏样式（仅在App环境中）
      uni.setNavigationBarColor({
        frontColor: style === 'light' ? '#ffffff' : '#000000',
        backgroundColor: theme === 'dark' ? '#121212' : '#2196F3',
        animation: {
          duration: 300,
          timingFunc: 'easeIn'
        }
      })

    } catch (error) {
      console.error('设置状态栏样式失败:', error)
    }
    // #endif

    // #ifdef H5
    // H5环境下通过CSS变量设置主题
    try {
      const root = document.documentElement
      if (root) {
        root.style.setProperty('--status-bar-color', theme === 'dark' ? '#121212' : '#2196F3')
        root.style.setProperty('--status-bar-text-color', theme === 'dark' ? '#ffffff' : '#000000')
      }
    } catch (error) {
      console.warn('H5环境设置主题变量失败:', error)
    }
    // #endif
  }

  /**
   * 获取当前主题
   * @returns {string} 当前主题名称
   */
  getCurrentTheme() {
    return this.currentTheme
  }

  /**
   * 获取主题显示名称
   * @param {string} theme - 主题名称
   * @returns {string} 显示名称
   */
  getThemeDisplayName(theme) {
    const themeMap = {
      auto: '跟随系统',
      light: '浅色模式',
      dark: '深色模式'
    }
    return themeMap[theme] || theme
  }

  /**
   * 切换到下一个主题
   */
  toggleTheme() {
    const themes = ['auto', 'light', 'dark']
    const currentIndex = themes.indexOf(this.currentTheme)
    const nextIndex = (currentIndex + 1) % themes.length
    this.setTheme(themes[nextIndex])
  }

  /**
   * 监听系统主题变化
   */
  watchSystemTheme() {
    try {
      // 监听系统主题变化（仅在支持的平台）
      uni.onThemeChange && uni.onThemeChange((res) => {
        if (this.currentTheme === 'auto') {
          this.applyTheme('auto')
        }
      })
    } catch (error) {
      console.error('监听系统主题变化失败:', error)
    }
  }

  /**
   * 获取主题CSS变量
   * @param {string} theme - 主题名称
   * @returns {object} CSS变量对象
   */
  getThemeVariables(theme) {
    const variables = {
      light: {
        '--bg-color': '#f8f9fa',
        '--text-color': '#333333',
        '--card-bg': '#ffffff'
      },
      dark: {
        '--bg-color': '#121212',
        '--text-color': '#ffffff',
        '--card-bg': '#1e1e1e'
      }
    }
    
    if (theme === 'auto') {
      // 检测系统主题
      try {
        const systemInfo = uni.getSystemInfoSync()
        return variables[systemInfo.theme] || variables.light
      } catch (error) {
        return variables.light
      }
    }
    
    return variables[theme] || variables.light
  }
}

// 创建全局实例
const themeManager = new ThemeManager()

export { ThemeManager, themeManager }
export default themeManager
